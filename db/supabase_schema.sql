-- db/supabase_schema.sql
-- PostgreSQL Schema for the Form D data storage system (Supabase compatible)

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";

-- ZIP files table to track downloaded ZIP files
CREATE TABLE IF NOT EXISTS zip_files (
    id SERIAL PRIMARY KEY,
    filename TEXT NOT NULL,
    url TEXT NOT NULL,
    date_str TEXT NOT NULL,  -- YYYYMMDD format
    download_date TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    file_size BIGINT,
    status TEXT NOT NULL DEFAULT 'downloaded',  -- downloaded, extracted, processed, error
    error_message TEXT,
    last_accessed TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    access_count INTEGER NOT NULL DEFAULT 1,
    is_placeholder BOOLEAN NOT NULL DEFAULT FALSE,
    UNIQUE(filename)
);

-- Form D filings table
CREATE TABLE IF NOT EXISTS form_d_filings (
    id SERIAL PRIMARY KEY,
    accession_number TEXT NOT NULL,
    file_number TEXT,
    issuer_name TEXT NOT NULL,
    filing_date TEXT NOT NULL,
    offering_amount DECIMAL(15,2),
    industry_group TEXT,
    issuer_city TEXT,
    issuer_state TEXT,
    issuer_zip TEXT,
    offering_type TEXT,
    minimum_investment DECIMAL(15,2),
    total_amount_sold DECIMAL(15,2),
    total_remaining DECIMAL(15,2),
    source_zip_id INTEGER,
    source_file TEXT,
    json_data JSONB NOT NULL,  -- Full JSON data as JSONB for better performance
    embedding_id TEXT,  -- ID in the vector store
    embedding VECTOR(1536),  -- OpenAI embedding dimension, adjust as needed
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(accession_number),
    FOREIGN KEY(source_zip_id) REFERENCES zip_files(id) ON DELETE SET NULL
);

-- Feed entries table
CREATE TABLE IF NOT EXISTS feed_entries (
    id SERIAL PRIMARY KEY,
    entry_id TEXT NOT NULL,
    title TEXT NOT NULL,
    link TEXT NOT NULL,
    published TIMESTAMPTZ NOT NULL,
    updated TIMESTAMPTZ NOT NULL,
    summary TEXT,
    content TEXT,
    filing_id INTEGER,
    embedding_id TEXT,  -- ID in the vector store
    embedding VECTOR(1536),  -- OpenAI embedding dimension
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    UNIQUE(entry_id),
    FOREIGN KEY(filing_id) REFERENCES form_d_filings(id) ON DELETE SET NULL
);

-- Analysis results table
CREATE TABLE IF NOT EXISTS analysis_results (
    id SERIAL PRIMARY KEY,
    filing_id INTEGER NOT NULL,
    relevance_score DECIMAL(3,2) NOT NULL CHECK (relevance_score >= 0 AND relevance_score <= 1),
    summary TEXT,
    email_draft TEXT,
    analysis_timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    model_name TEXT,
    prompt_used TEXT,
    metadata JSONB,  -- Additional analysis metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    FOREIGN KEY(filing_id) REFERENCES form_d_filings(id) ON DELETE CASCADE
);

-- Cache management table
CREATE TABLE IF NOT EXISTS cache_management (
    id SERIAL PRIMARY KEY,
    cache_type TEXT NOT NULL,  -- zip, json, embedding
    item_id INTEGER NOT NULL,  -- reference to the item in its respective table
    last_accessed TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    access_count INTEGER NOT NULL DEFAULT 1,
    priority INTEGER NOT NULL DEFAULT 0,  -- Higher number = higher priority
    size_bytes BIGINT,
    metadata JSONB,  -- Additional cache metadata
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_zip_files_date ON zip_files(date_str);
CREATE INDEX IF NOT EXISTS idx_zip_files_status ON zip_files(status);
CREATE INDEX IF NOT EXISTS idx_zip_files_last_accessed ON zip_files(last_accessed);

CREATE INDEX IF NOT EXISTS idx_form_d_filings_date ON form_d_filings(filing_date);
CREATE INDEX IF NOT EXISTS idx_form_d_filings_issuer ON form_d_filings(issuer_name);
CREATE INDEX IF NOT EXISTS idx_form_d_filings_source_zip ON form_d_filings(source_zip_id);
CREATE INDEX IF NOT EXISTS idx_form_d_filings_offering_amount ON form_d_filings(offering_amount);
CREATE INDEX IF NOT EXISTS idx_form_d_filings_industry ON form_d_filings(industry_group);

-- JSONB indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_form_d_filings_json_data ON form_d_filings USING GIN(json_data);

CREATE INDEX IF NOT EXISTS idx_feed_entries_published ON feed_entries(published);
CREATE INDEX IF NOT EXISTS idx_feed_entries_filing_id ON feed_entries(filing_id);

CREATE INDEX IF NOT EXISTS idx_analysis_results_filing_id ON analysis_results(filing_id);
CREATE INDEX IF NOT EXISTS idx_analysis_results_relevance ON analysis_results(relevance_score);
CREATE INDEX IF NOT EXISTS idx_analysis_results_timestamp ON analysis_results(analysis_timestamp);

CREATE INDEX IF NOT EXISTS idx_cache_management_type ON cache_management(cache_type);
CREATE INDEX IF NOT EXISTS idx_cache_management_last_accessed ON cache_management(last_accessed);
CREATE INDEX IF NOT EXISTS idx_cache_management_priority ON cache_management(priority);

-- Vector similarity search indexes (if using pgvector)
CREATE INDEX IF NOT EXISTS idx_form_d_filings_embedding ON form_d_filings USING ivfflat (embedding vector_cosine_ops);
CREATE INDEX IF NOT EXISTS idx_feed_entries_embedding ON feed_entries USING ivfflat (embedding vector_cosine_ops);
