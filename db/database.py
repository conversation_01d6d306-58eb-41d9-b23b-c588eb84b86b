#!/usr/bin/env python3
"""
Database Manager for Form D Data

Provides a centralized interface for database operations:
1. Initializing and maintaining the SQLite database
2. Tracking ZIP files and their status
3. Storing and retrieving Form D filings
4. Managing the cache with TTL and LRU policies
"""

import os
import sqlite3
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from datetime import datetime, timedelta

class DatabaseManager:
    """
    Database manager for Form D data storage and caching.
    """

    def __init__(self, db_path: str = "db/formd.db", schema_path: str = "db/schema.sql"):
        """
        Initialize the database manager.

        Args:
            db_path: Path to the SQLite database file
            schema_path: Path to the SQL schema file
        """
        self.db_path = Path(db_path)
        self.schema_path = Path(schema_path)

        # Set up logging first
        self.logger = logging.getLogger(__name__)

        # Create directory if it doesn't exist
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # Initialize database
        self._initialize_db()

    def _initialize_db(self) -> None:
        """Initialize the database with the schema."""
        # Check if database exists
        db_exists = self.db_path.exists()

        # Connect to database
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Enable foreign keys
        cursor.execute("PRAGMA foreign_keys = ON;")

        # Create tables if they don't exist
        if not db_exists:
            self.logger.info(f"Creating new database at {self.db_path}")
            with open(self.schema_path, 'r') as f:
                schema_sql = f.read()
                cursor.executescript(schema_sql)
            conn.commit()

        # Close connection
        conn.close()

    def _get_connection(self) -> sqlite3.Connection:
        """Get a database connection with foreign keys enabled."""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Return rows as dictionaries
        cursor = conn.cursor()
        cursor.execute("PRAGMA foreign_keys = ON;")
        return conn

    # ZIP file management

    def add_zip_file(self, filename: str, url: str, date_str: str,
                    file_size: Optional[int] = None,
                    status: str = "downloaded",
                    is_placeholder: bool = False) -> int:
        """
        Add a ZIP file to the database.

        Args:
            filename: Name of the ZIP file
            url: URL where the ZIP file was downloaded from
            date_str: Date string in YYYYMMDD format
            file_size: Size of the file in bytes
            status: Status of the ZIP file (downloaded, extracted, processed, error)
            is_placeholder: Whether this is a placeholder entry

        Returns:
            ID of the inserted ZIP file
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                INSERT INTO zip_files
                (filename, url, date_str, file_size, status, is_placeholder)
                VALUES (?, ?, ?, ?, ?, ?)
                """,
                (filename, url, date_str, file_size, status, is_placeholder)
            )
            conn.commit()
            zip_id = cursor.lastrowid
            self.logger.info(f"Added ZIP file {filename} with ID {zip_id}")
            return zip_id
        except sqlite3.IntegrityError:
            # ZIP file already exists, update it
            cursor.execute(
                """
                UPDATE zip_files
                SET url = ?, date_str = ?, file_size = ?, status = ?,
                    is_placeholder = ?, last_accessed = CURRENT_TIMESTAMP,
                    access_count = access_count + 1
                WHERE filename = ?
                """,
                (url, date_str, file_size, status, is_placeholder, filename)
            )
            conn.commit()

            # Get the ID of the updated ZIP file
            cursor.execute("SELECT id FROM zip_files WHERE filename = ?", (filename,))
            zip_id = cursor.fetchone()["id"]
            self.logger.info(f"Updated ZIP file {filename} with ID {zip_id}")
            return zip_id
        finally:
            conn.close()

    def update_zip_status(self, zip_id: int, status: str,
                         error_message: Optional[str] = None) -> None:
        """
        Update the status of a ZIP file.

        Args:
            zip_id: ID of the ZIP file
            status: New status
            error_message: Error message if status is 'error'
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                UPDATE zip_files
                SET status = ?, error_message = ?, last_accessed = CURRENT_TIMESTAMP
                WHERE id = ?
                """,
                (status, error_message, zip_id)
            )
            conn.commit()
            self.logger.info(f"Updated ZIP file {zip_id} status to {status}")
        finally:
            conn.close()

    def get_zip_file(self, zip_id: Optional[int] = None,
                    filename: Optional[str] = None,
                    date_str: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get a ZIP file from the database.

        Args:
            zip_id: ID of the ZIP file
            filename: Name of the ZIP file
            date_str: Date string in YYYYMMDD format

        Returns:
            ZIP file data or None if not found
        """
        if not any([zip_id, filename, date_str]):
            raise ValueError("Must provide at least one of zip_id, filename, or date_str")

        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            if zip_id:
                cursor.execute("SELECT * FROM zip_files WHERE id = ?", (zip_id,))
            elif filename:
                cursor.execute("SELECT * FROM zip_files WHERE filename = ?", (filename,))
            elif date_str:
                cursor.execute("SELECT * FROM zip_files WHERE date_str = ?", (date_str,))

            row = cursor.fetchone()

            if row:
                # Update last accessed time and access count
                cursor.execute(
                    """
                    UPDATE zip_files
                    SET last_accessed = CURRENT_TIMESTAMP, access_count = access_count + 1
                    WHERE id = ?
                    """,
                    (row["id"],)
                )
                conn.commit()

                return dict(row)
            return None
        finally:
            conn.close()

    # Form D filing management

    def add_form_d_filing(self, accession_number: str, issuer_name: str,
                         filing_date: str, json_data: Dict[str, Any],
                         source_zip_id: Optional[int] = None,
                         source_file: Optional[str] = None) -> int:
        """
        Add a Form D filing to the database.

        Args:
            accession_number: SEC accession number
            issuer_name: Name of the issuer
            filing_date: Filing date
            json_data: Full JSON data of the filing
            source_zip_id: ID of the source ZIP file
            source_file: Path to the source file

        Returns:
            ID of the inserted filing
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # Extract additional fields from JSON data
            file_number = json_data.get("fileNumber") or json_data.get("file_number")
            offering_amount = self._parse_numeric(json_data.get("offeringAmount") or json_data.get("offering_amount"))
            industry_group = json_data.get("industryGroup") or json_data.get("industry_group")
            issuer_city = json_data.get("issuerCity") or json_data.get("issuer_city")
            issuer_state = json_data.get("issuerState") or json_data.get("issuer_state")
            issuer_zip = json_data.get("issuerZipCode") or json_data.get("issuer_zip_code")
            offering_type = json_data.get("offeringType") or json_data.get("offering_type")
            minimum_investment = self._parse_numeric(json_data.get("minimumInvestmentAccepted") or json_data.get("minimum_investment_accepted"))
            total_amount_sold = self._parse_numeric(json_data.get("totalAmountSold") or json_data.get("total_amount_sold"))
            total_remaining = self._parse_numeric(json_data.get("totalRemaining") or json_data.get("total_remaining"))

            # Convert JSON data to string
            json_str = json.dumps(json_data)

            cursor.execute(
                """
                INSERT INTO form_d_filings
                (accession_number, file_number, issuer_name, filing_date,
                offering_amount, industry_group, issuer_city, issuer_state,
                issuer_zip, offering_type, minimum_investment, total_amount_sold,
                total_remaining, source_zip_id, source_file, json_data)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                (accession_number, file_number, issuer_name, filing_date,
                offering_amount, industry_group, issuer_city, issuer_state,
                issuer_zip, offering_type, minimum_investment, total_amount_sold,
                total_remaining, source_zip_id, source_file, json_str)
            )
            conn.commit()
            filing_id = cursor.lastrowid
            self.logger.info(f"Added Form D filing {accession_number} with ID {filing_id}")
            return filing_id
        except sqlite3.IntegrityError:
            # Filing already exists, update it
            cursor.execute(
                """
                UPDATE form_d_filings
                SET file_number = ?, issuer_name = ?, filing_date = ?,
                    offering_amount = ?, industry_group = ?, issuer_city = ?,
                    issuer_state = ?, issuer_zip = ?, offering_type = ?,
                    minimum_investment = ?, total_amount_sold = ?,
                    total_remaining = ?, source_zip_id = ?, source_file = ?,
                    json_data = ?, updated_at = CURRENT_TIMESTAMP
                WHERE accession_number = ?
                """,
                (file_number, issuer_name, filing_date,
                offering_amount, industry_group, issuer_city,
                issuer_state, issuer_zip, offering_type,
                minimum_investment, total_amount_sold,
                total_remaining, source_zip_id, source_file,
                json_str, accession_number)
            )
            conn.commit()

            # Get the ID of the updated filing
            cursor.execute("SELECT id FROM form_d_filings WHERE accession_number = ?", (accession_number,))
            filing_id = cursor.fetchone()["id"]
            self.logger.info(f"Updated Form D filing {accession_number} with ID {filing_id}")
            return filing_id
        finally:
            conn.close()

    def _parse_numeric(self, value: Any) -> Optional[float]:
        """Parse a numeric value from various formats."""
        if value is None:
            return None

        if isinstance(value, (int, float)):
            return float(value)

        try:
            # Remove non-numeric characters except decimal point
            if isinstance(value, str):
                value = ''.join(c for c in value if c.isdigit() or c == '.')
                return float(value) if value else None
            return None
        except (ValueError, TypeError):
            return None

    def get_form_d_filing(self, filing_id: Optional[int] = None,
                         accession_number: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """
        Get a Form D filing from the database.

        Args:
            filing_id: ID of the filing
            accession_number: SEC accession number

        Returns:
            Filing data or None if not found
        """
        if not any([filing_id, accession_number]):
            raise ValueError("Must provide either filing_id or accession_number")

        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            if filing_id:
                cursor.execute("SELECT * FROM form_d_filings WHERE id = ?", (filing_id,))
            else:
                cursor.execute("SELECT * FROM form_d_filings WHERE accession_number = ?", (accession_number,))

            row = cursor.fetchone()

            if row:
                # Parse JSON data
                result = dict(row)
                result["json_data"] = json.loads(result["json_data"])
                return result
            return None
        finally:
            conn.close()

    def search_form_d_filings(self,
                             issuer_name: Optional[str] = None,
                             filing_date_start: Optional[str] = None,
                             filing_date_end: Optional[str] = None,
                             industry_group: Optional[str] = None,
                             min_offering_amount: Optional[float] = None,
                             max_offering_amount: Optional[float] = None,
                             limit: int = 100,
                             offset: int = 0) -> List[Dict[str, Any]]:
        """
        Search Form D filings based on criteria.

        Args:
            issuer_name: Partial issuer name to search for
            filing_date_start: Start date for filing date range
            filing_date_end: End date for filing date range
            industry_group: Industry group to filter by
            min_offering_amount: Minimum offering amount
            max_offering_amount: Maximum offering amount
            limit: Maximum number of results to return
            offset: Offset for pagination

        Returns:
            List of matching filings
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            query = "SELECT * FROM form_d_filings WHERE 1=1"
            params = []

            if issuer_name:
                query += " AND issuer_name LIKE ?"
                params.append(f"%{issuer_name}%")

            if filing_date_start:
                query += " AND filing_date >= ?"
                params.append(filing_date_start)

            if filing_date_end:
                query += " AND filing_date <= ?"
                params.append(filing_date_end)

            if industry_group:
                query += " AND industry_group = ?"
                params.append(industry_group)

            if min_offering_amount is not None:
                query += " AND offering_amount >= ?"
                params.append(min_offering_amount)

            if max_offering_amount is not None:
                query += " AND offering_amount <= ?"
                params.append(max_offering_amount)

            query += " ORDER BY filing_date DESC LIMIT ? OFFSET ?"
            params.extend([limit, offset])

            cursor.execute(query, params)
            rows = cursor.fetchall()

            results = []
            for row in rows:
                result = dict(row)
                result["json_data"] = json.loads(result["json_data"])
                results.append(result)

            return results
        finally:
            conn.close()

    # Feed entry management

    def add_feed_entry(self, entry_id: str, title: str, link: str,
                      published: str, updated: str, summary: Optional[str] = None,
                      content: Optional[str] = None) -> int:
        """
        Add a feed entry to the database.

        Args:
            entry_id: Unique ID of the entry
            title: Entry title
            link: Entry link
            published: Published date
            updated: Updated date
            summary: Entry summary
            content: Entry content

        Returns:
            ID of the inserted entry
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                INSERT INTO feed_entries
                (entry_id, title, link, published, updated, summary, content)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                (entry_id, title, link, published, updated, summary, content)
            )
            conn.commit()
            entry_id_db = cursor.lastrowid
            self.logger.info(f"Added feed entry {entry_id} with ID {entry_id_db}")
            return entry_id_db
        except sqlite3.IntegrityError:
            # Entry already exists, update it
            cursor.execute(
                """
                UPDATE feed_entries
                SET title = ?, link = ?, published = ?, updated = ?,
                    summary = ?, content = ?, updated_at = CURRENT_TIMESTAMP
                WHERE entry_id = ?
                """,
                (title, link, published, updated, summary, content, entry_id)
            )
            conn.commit()

            # Get the ID of the updated entry
            cursor.execute("SELECT id FROM feed_entries WHERE entry_id = ?", (entry_id,))
            entry_id_db = cursor.fetchone()["id"]
            self.logger.info(f"Updated feed entry {entry_id} with ID {entry_id_db}")
            return entry_id_db
        finally:
            conn.close()

    def link_feed_entry_to_filing(self, feed_entry_id: int, filing_id: int) -> None:
        """
        Link a feed entry to a Form D filing.

        Args:
            feed_entry_id: ID of the feed entry
            filing_id: ID of the Form D filing
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute(
                """
                UPDATE feed_entries
                SET filing_id = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
                """,
                (filing_id, feed_entry_id)
            )
            conn.commit()
            self.logger.info(f"Linked feed entry {feed_entry_id} to filing {filing_id}")
        finally:
            conn.close()

    # Analysis results management

    def add_analysis_result(self, filing_id: int, relevance_score: float,
                           summary: Optional[str] = None, email_draft: Optional[str] = None,
                           analysis_timestamp: Optional[str] = None,
                           model_name: Optional[str] = None,
                           prompt_used: Optional[str] = None) -> int:
        """
        Add an analysis result to the database.

        Args:
            filing_id: ID of the Form D filing
            relevance_score: Relevance score (0.0-1.0)
            summary: Summary of the filing
            email_draft: Draft email content
            analysis_timestamp: Timestamp of the analysis
            model_name: Name of the model used
            prompt_used: Prompt used for analysis

        Returns:
            ID of the inserted analysis result
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # Use current timestamp if none provided
            if analysis_timestamp is None:
                analysis_timestamp = datetime.now().isoformat()

            cursor.execute(
                """
                INSERT INTO analysis_results
                (filing_id, relevance_score, summary, email_draft,
                analysis_timestamp, model_name, prompt_used)
                VALUES (?, ?, ?, ?, ?, ?, ?)
                """,
                (filing_id, relevance_score, summary, email_draft,
                analysis_timestamp, model_name, prompt_used)
            )
            conn.commit()
            result_id = cursor.lastrowid
            self.logger.info(f"Added analysis result for filing {filing_id} with ID {result_id}")
            return result_id
        finally:
            conn.close()

    # Cache management

    def update_cache_item(self, cache_type: str, item_id: int,
                         size_bytes: Optional[int] = None,
                         priority: Optional[int] = None) -> None:
        """
        Update or add a cache item.

        Args:
            cache_type: Type of cache item (zip, json, embedding)
            item_id: ID of the item in its respective table
            size_bytes: Size of the item in bytes
            priority: Priority of the item (higher = more important)
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            # Check if item exists
            cursor.execute(
                "SELECT id FROM cache_management WHERE cache_type = ? AND item_id = ?",
                (cache_type, item_id)
            )
            row = cursor.fetchone()

            if row:
                # Update existing item
                update_query = """
                UPDATE cache_management
                SET last_accessed = CURRENT_TIMESTAMP,
                    access_count = access_count + 1,
                    updated_at = CURRENT_TIMESTAMP
                """
                params = []

                if size_bytes is not None:
                    update_query += ", size_bytes = ?"
                    params.append(size_bytes)

                if priority is not None:
                    update_query += ", priority = ?"
                    params.append(priority)

                update_query += " WHERE id = ?"
                params.append(row["id"])

                cursor.execute(update_query, params)
            else:
                # Add new item
                cursor.execute(
                    """
                    INSERT INTO cache_management
                    (cache_type, item_id, size_bytes, priority)
                    VALUES (?, ?, ?, ?)
                    """,
                    (cache_type, item_id, size_bytes, priority or 0)
                )

            conn.commit()
        finally:
            conn.close()

    def get_cache_items_to_clean(self, cache_type: str,
                                older_than_days: int = 30,
                                min_access_count: int = 5,
                                exclude_priority_above: int = 5,
                                limit: int = 100) -> List[Dict[str, Any]]:
        """
        Get cache items that can be cleaned up.

        Args:
            cache_type: Type of cache items to clean (zip, json, embedding)
            older_than_days: Only consider items older than this many days
            min_access_count: Only consider items accessed fewer than this many times
            exclude_priority_above: Exclude items with priority above this value
            limit: Maximum number of items to return

        Returns:
            List of cache items to clean up
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cutoff_date = (datetime.now() - timedelta(days=older_than_days)).strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute(
                """
                SELECT * FROM cache_management
                WHERE cache_type = ?
                AND last_accessed < ?
                AND access_count < ?
                AND priority <= ?
                ORDER BY priority ASC, last_accessed ASC
                LIMIT ?
                """,
                (cache_type, cutoff_date, min_access_count, exclude_priority_above, limit)
            )

            return [dict(row) for row in cursor.fetchall()]
        finally:
            conn.close()

    def remove_cache_item(self, cache_id: int) -> None:
        """
        Remove a cache item.

        Args:
            cache_id: ID of the cache item
        """
        conn = self._get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute("DELETE FROM cache_management WHERE id = ?", (cache_id,))
            conn.commit()
            self.logger.info(f"Removed cache item {cache_id}")
        finally:
            conn.close()
