#!/usr/bin/env python3
"""
Evaluate Fix Progress Script

This script evaluates whether the NULL value fixes have been working
by comparing current NULL counts with the baseline and checking recently updated records.
"""

import os
import sys
import logging
from typing import Dict, List, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from db.supabase_manager import SupabaseDatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FixProgressEvaluator:
    """Evaluates the progress and effectiveness of the NULL value fixes."""
    
    def __init__(self):
        """Initialize the evaluator with database connection."""
        try:
            self.db = SupabaseDatabaseManager()
            logger.info("✅ Connected to Supabase database")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            raise
    
    def get_current_null_counts(self) -> Dict[str, Any]:
        """Get current NULL counts for all critical fields."""
        cursor = self.db.get_cursor()
        
        try:
            # Get total record count
            cursor.execute("SELECT COUNT(*) as total FROM form_d_filings")
            total_records = cursor.fetchone()['total']
            
            # Get NULL counts for each field
            fields_to_check = [
                'issuer_city', 'issuer_state', 'issuer_zip', 'offering_amount',
                'minimum_investment', 'total_amount_sold', 'total_remaining'
            ]
            
            null_counts = {}
            null_percentages = {}
            
            for field in fields_to_check:
                cursor.execute(f"""
                    SELECT COUNT(*) as null_count 
                    FROM form_d_filings 
                    WHERE {field} IS NULL
                """)
                null_count = cursor.fetchone()['null_count']
                null_percentage = (null_count / total_records) * 100 if total_records > 0 else 0
                
                null_counts[field] = null_count
                null_percentages[field] = round(null_percentage, 2)
            
            return {
                'total_records': total_records,
                'null_counts': null_counts,
                'null_percentages': null_percentages,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get current NULL counts: {e}")
            return {}
    
    def check_recently_updated_records(self, hours_back: int = 1) -> Dict[str, Any]:
        """Check records that have been updated recently to see if fixes worked."""
        cursor = self.db.get_cursor()
        
        try:
            # Get records updated in the last hour
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_updated,
                    COUNT(CASE WHEN issuer_city IS NOT NULL THEN 1 END) as city_filled,
                    COUNT(CASE WHEN issuer_state IS NOT NULL THEN 1 END) as state_filled,
                    COUNT(CASE WHEN issuer_zip IS NOT NULL THEN 1 END) as zip_filled,
                    COUNT(CASE WHEN offering_amount IS NOT NULL THEN 1 END) as amount_filled,
                    COUNT(CASE WHEN minimum_investment IS NOT NULL THEN 1 END) as min_investment_filled,
                    COUNT(CASE WHEN total_amount_sold IS NOT NULL THEN 1 END) as amount_sold_filled,
                    COUNT(CASE WHEN total_remaining IS NOT NULL THEN 1 END) as remaining_filled
                FROM form_d_filings
                WHERE updated_at >= NOW() - INTERVAL '%s hours'
                  AND updated_at > created_at
            """, (hours_back,))
            
            result = cursor.fetchone()
            
            if result['total_updated'] == 0:
                return {
                    'total_updated': 0,
                    'message': f"No records updated in the last {hours_back} hour(s)"
                }
            
            # Calculate fill rates for recently updated records
            total = result['total_updated']
            fill_rates = {}
            
            fields = {
                'issuer_city': result['city_filled'],
                'issuer_state': result['state_filled'],
                'issuer_zip': result['zip_filled'],
                'offering_amount': result['amount_filled'],
                'minimum_investment': result['min_investment_filled'],
                'total_amount_sold': result['amount_sold_filled'],
                'total_remaining': result['remaining_filled']
            }
            
            for field, filled_count in fields.items():
                fill_rates[field] = {
                    'filled_count': filled_count,
                    'fill_rate_percent': round((filled_count / total) * 100, 2) if total > 0 else 0
                }
            
            return {
                'total_updated': total,
                'fill_rates': fill_rates,
                'hours_back': hours_back,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to check recently updated records: {e}")
            return {}
    
    def sample_fixed_records(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get a sample of records that appear to have been fixed."""
        cursor = self.db.get_cursor()
        
        try:
            cursor.execute("""
                SELECT 
                    id, accession_number, issuer_name, 
                    issuer_city, issuer_state, issuer_zip,
                    offering_amount, minimum_investment, 
                    total_amount_sold, total_remaining,
                    updated_at, created_at
                FROM form_d_filings
                WHERE updated_at > created_at
                  AND updated_at >= NOW() - INTERVAL '2 hours'
                  AND (issuer_city IS NOT NULL 
                       OR issuer_state IS NOT NULL 
                       OR issuer_zip IS NOT NULL
                       OR offering_amount IS NOT NULL)
                ORDER BY updated_at DESC
                LIMIT %s
            """, (limit,))
            
            records = cursor.fetchall()
            return [dict(record) for record in records]
            
        except Exception as e:
            logger.error(f"❌ Failed to get sample fixed records: {e}")
            return []
    
    def compare_with_baseline(self) -> Dict[str, Any]:
        """Compare current state with the baseline from our previous evaluation."""
        # Baseline from our previous evaluation
        baseline = {
            'total_records': 284272,
            'null_counts': {
                'offering_amount': 119824,
                'issuer_city': 283369,
                'issuer_state': 283369,
                'issuer_zip': 283369,
                'minimum_investment': 283780,
                'total_amount_sold': 283503,
                'total_remaining': 284016
            },
            'null_percentages': {
                'offering_amount': 42.15,
                'issuer_city': 99.68,
                'issuer_state': 99.68,
                'issuer_zip': 99.68,
                'minimum_investment': 99.83,
                'total_amount_sold': 99.73,
                'total_remaining': 99.91
            }
        }
        
        current = self.get_current_null_counts()
        
        if not current:
            return {'error': 'Failed to get current counts'}
        
        comparison = {
            'baseline': baseline,
            'current': current,
            'improvements': {},
            'summary': {}
        }
        
        # Calculate improvements
        for field in baseline['null_counts'].keys():
            if field in current['null_counts']:
                baseline_nulls = baseline['null_counts'][field]
                current_nulls = current['null_counts'][field]
                records_fixed = baseline_nulls - current_nulls
                
                baseline_pct = baseline['null_percentages'][field]
                current_pct = current['null_percentages'][field]
                percentage_improvement = baseline_pct - current_pct
                
                comparison['improvements'][field] = {
                    'records_fixed': records_fixed,
                    'percentage_improvement': round(percentage_improvement, 2),
                    'baseline_nulls': baseline_nulls,
                    'current_nulls': current_nulls,
                    'baseline_pct': baseline_pct,
                    'current_pct': current_pct
                }
        
        # Summary statistics
        total_records_fixed = sum(imp['records_fixed'] for imp in comparison['improvements'].values() if imp['records_fixed'] > 0)
        fields_improved = sum(1 for imp in comparison['improvements'].values() if imp['records_fixed'] > 0)
        
        comparison['summary'] = {
            'total_records_fixed': total_records_fixed,
            'fields_improved': fields_improved,
            'total_fields_checked': len(comparison['improvements'])
        }
        
        return comparison
    
    def generate_progress_report(self) -> Dict[str, Any]:
        """Generate a comprehensive progress report."""
        logger.info("📊 Generating fix progress report...")
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'current_state': self.get_current_null_counts(),
            'recent_updates': self.check_recently_updated_records(hours_back=2),
            'sample_fixed_records': self.sample_fixed_records(limit=5),
            'baseline_comparison': self.compare_with_baseline()
        }
        
        return report
    
    def print_progress_report(self, report: Dict[str, Any]) -> None:
        """Print a formatted progress report."""
        print("\n" + "="*80)
        print("📈 DATABASE NULL VALUE FIX PROGRESS REPORT")
        print("="*80)
        print(f"📅 Generated: {report['timestamp']}")
        print()
        
        # Current state
        current = report['current_state']
        if current:
            print("📊 CURRENT DATABASE STATE:")
            print(f"   Total records: {current['total_records']:,}")
            print("   Current NULL percentages:")
            for field, pct in current['null_percentages'].items():
                status = "🟢" if pct < 10 else "🟡" if pct < 50 else "🔴"
                print(f"      {status} {field}: {pct}%")
            print()
        
        # Recent updates
        recent = report['recent_updates']
        if recent and recent.get('total_updated', 0) > 0:
            print("🔄 RECENT UPDATE ANALYSIS:")
            print(f"   Records updated in last {recent['hours_back']} hours: {recent['total_updated']:,}")
            print("   Fill rates for updated records:")
            for field, data in recent['fill_rates'].items():
                print(f"      • {field}: {data['filled_count']:,} records ({data['fill_rate_percent']}% filled)")
            print()
        else:
            print("🔄 RECENT UPDATE ANALYSIS:")
            print("   No recent updates found")
            print()
        
        # Baseline comparison
        comparison = report['baseline_comparison']
        if 'improvements' in comparison:
            print("📈 IMPROVEMENT ANALYSIS:")
            print("   Changes since baseline:")
            
            any_improvements = False
            for field, imp in comparison['improvements'].items():
                if imp['records_fixed'] > 0:
                    any_improvements = True
                    print(f"      ✅ {field}: {imp['records_fixed']:,} records fixed "
                          f"({imp['baseline_pct']}% → {imp['current_pct']}%)")
                elif imp['records_fixed'] < 0:
                    print(f"      ⚠️  {field}: {abs(imp['records_fixed']):,} more NULLs "
                          f"({imp['baseline_pct']}% → {imp['current_pct']}%)")
            
            if not any_improvements:
                print("      ⚠️  No improvements detected yet")
            
            summary = comparison['summary']
            print(f"\n   Summary: {summary['total_records_fixed']:,} total record fixes across "
                  f"{summary['fields_improved']} fields")
            print()
        
        # Sample fixed records
        samples = report['sample_fixed_records']
        if samples:
            print("🔍 SAMPLE OF RECENTLY FIXED RECORDS:")
            for i, record in enumerate(samples[:3], 1):
                print(f"   {i}. {record['issuer_name'][:50]}...")
                print(f"      Accession: {record['accession_number']}")
                if record['issuer_city']:
                    print(f"      Location: {record['issuer_city']}, {record['issuer_state']} {record['issuer_zip']}")
                if record['offering_amount']:
                    print(f"      Offering: ${record['offering_amount']:,.2f}")
                print(f"      Updated: {record['updated_at']}")
                print()
        else:
            print("🔍 SAMPLE OF RECENTLY FIXED RECORDS:")
            print("   No recently fixed records found")
            print()
        
        print("="*80)

def main():
    """Main function to run the progress evaluation."""
    try:
        evaluator = FixProgressEvaluator()
        report = evaluator.generate_progress_report()
        evaluator.print_progress_report(report)
        
        # Save report
        import json
        report_file = f"fix_progress_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved to: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ Failed to run progress evaluation: {e}")
        sys.exit(1)
    finally:
        if 'evaluator' in locals():
            evaluator.db.close()

if __name__ == "__main__":
    main()
