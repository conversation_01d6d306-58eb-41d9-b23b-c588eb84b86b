# Supabase MCP Server - Task List

## Current Tasks
- [x] Create initial project structure (2025-03-29)
- [x] Implement MCP server with Stdio transport (2025-03-29)
- [x] Implement Supabase client integration (2025-03-29)
- [x] Create tool for reading records from tables (2025-03-29)
- [x] Create tool for creating records in tables (2025-03-29)
- [x] Create tool for updating records in tables (2025-03-29)
- [x] Create tool for deleting records from tables (2025-03-29)
- [X] Update README.md with setup and usage instructions (2025-03-29)

## Upcoming Tasks
- [X] Add unit tests for all tools (2025-03-29)
- [X] Implement error handling and logging (2025-03-29)
- [ ] Add support for pagination in read operations
- [ ] Add support for filtering in read operations
- [ ] Add support for sorting in read operations
- [ ] Add support for joins in read operations
- [ ] Implement schema validation for input data

## Discovered During Work
- [ ] Consider adding a tool for executing raw SQL queries
- [ ] Consider adding a tool for listing available tables
- [ ] Consider adding a tool for reading table schemas
