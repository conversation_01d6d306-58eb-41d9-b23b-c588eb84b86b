#!/usr/bin/env python3
"""
Simple High-Performance Database NULL Value Fix Script

This script uses the existing Supabase manager to fix NULL values efficiently
with larger batch sizes and progress tracking.
"""

import os
import sys
import json
import logging
import time
from typing import Dict, Any
from datetime import datetime
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from db.supabase_manager import SupabaseDatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleHighPerformanceNullFixer:
    """Simple high-performance NULL value fixer using existing Supabase manager."""
    
    def __init__(self):
        """Initialize the fixer."""
        try:
            self.db = SupabaseDatabaseManager()
            self.progress_file = "simple_null_fix_progress.json"
            self.progress = self.load_progress()
            logger.info("✅ Connected to Supabase database")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            raise
    
    def load_progress(self) -> Dict[str, Any]:
        """Load progress from checkpoint file."""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    progress = json.load(f)
                logger.info(f"📂 Loaded progress: {progress.get('processed', 0):,} records processed")
                return progress
            except Exception as e:
                logger.warning(f"⚠️ Failed to load progress file: {e}")
        
        return {
            'processed': 0,
            'last_id': 0,
            'start_time': None,
            'fixed_counts': {
                'issuer_city': 0, 'issuer_state': 0, 'issuer_zip': 0,
                'offering_amount': 0, 'minimum_investment': 0,
                'total_amount_sold': 0, 'total_remaining': 0
            }
        }
    
    def save_progress(self) -> None:
        """Save current progress to checkpoint file."""
        try:
            with open(self.progress_file, 'w') as f:
                json.dump(self.progress, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"❌ Failed to save progress: {e}")
    
    def extract_all_data_from_json(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Single-pass extraction of all fields from JSON data.
        
        Args:
            json_data: The full JSON data from the filing
            
        Returns:
            Dictionary with all extracted data
        """
        def safe_numeric(value):
            """Convert value to float, handling various formats."""
            if value is None or value == '' or str(value).lower() in ['indefinite', 'n/a', 'none', 'unlimited']:
                return None
            try:
                clean_value = str(value).replace(',', '').replace('$', '').replace(' ', '')
                return float(clean_value)
            except (ValueError, TypeError):
                return None
        
        extracted = {
            'issuer_city': None, 'issuer_state': None, 'issuer_zip': None,
            'offering_amount': None, 'minimum_investment': None,
            'total_amount_sold': None, 'total_remaining': None
        }
        
        # Enhanced issuer data extraction (proven 100% success rate)
        issuer_paths = [
            ['issuers', 0], ['issuer'], ['primaryIssuer'], ['relatedPersons', 0]
        ]
        
        for path in issuer_paths:
            current = json_data
            try:
                for key in path:
                    if isinstance(key, int):
                        if isinstance(current, list) and len(current) > key:
                            current = current[key]
                        else:
                            break
                    else:
                        if isinstance(current, dict) and key in current:
                            current = current[key]
                        else:
                            break
                else:
                    if isinstance(current, dict):
                        # Extract issuer fields with all variations
                        city_fields = ['CITY', 'city', 'issuerCity', 'City']
                        state_fields = ['STATEORCOUNTRY', 'stateOrCountry', 'state', 'State', 'issuerState']
                        zip_fields = ['ZIPCODE', 'zipCode', 'zip', 'Zip', 'postalCode', 'issuerZip']
                        
                        for field in city_fields:
                            if field in current and current[field]:
                                extracted['issuer_city'] = str(current[field]).strip()
                                break
                        
                        for field in state_fields:
                            if field in current and current[field]:
                                extracted['issuer_state'] = str(current[field]).strip()
                                break
                        
                        for field in zip_fields:
                            if field in current and current[field]:
                                extracted['issuer_zip'] = str(current[field]).strip()
                                break
                        
                        if any([extracted['issuer_city'], extracted['issuer_state'], extracted['issuer_zip']]):
                            break
            except (KeyError, IndexError, TypeError):
                continue
        
        # Enhanced offering data extraction (50-93% success rate)
        offering_paths = [['offeringData'], ['offering'], ['']]  # '' means top level
        
        for path in offering_paths:
            current = json_data
            try:
                if path and path[0]:
                    current = current[path[0]]
                
                if isinstance(current, dict):
                    # Extract all offering fields
                    amount_fields = ['TOTALOFFERINGAMOUNT', 'totalOfferingAmount', 'offeringAmount']
                    min_inv_fields = ['MINIMUMINVESTMENTACCEPTED', 'minimumInvestmentAccepted', 'minimumInvestment']
                    sold_fields = ['TOTALAMOUNTSOLD', 'totalAmountSold', 'amountSold']
                    remaining_fields = ['TOTALREMAINING', 'totalRemaining', 'remainingAmount']
                    
                    if not extracted['offering_amount']:
                        for field in amount_fields:
                            if field in current:
                                extracted['offering_amount'] = safe_numeric(current[field])
                                if extracted['offering_amount'] is not None:
                                    break
                    
                    if not extracted['minimum_investment']:
                        for field in min_inv_fields:
                            if field in current:
                                extracted['minimum_investment'] = safe_numeric(current[field])
                                if extracted['minimum_investment'] is not None:
                                    break
                    
                    if not extracted['total_amount_sold']:
                        for field in sold_fields:
                            if field in current:
                                extracted['total_amount_sold'] = safe_numeric(current[field])
                                if extracted['total_amount_sold'] is not None:
                                    break
                    
                    if not extracted['total_remaining']:
                        for field in remaining_fields:
                            if field in current:
                                extracted['total_remaining'] = safe_numeric(current[field])
                                if extracted['total_remaining'] is not None:
                                    break
            except (KeyError, TypeError):
                continue
        
        return extracted
    
    def process_batch(self, batch_size: int = 2000) -> int:
        """
        Process a batch of records.
        
        Args:
            batch_size: Number of records to process
            
        Returns:
            Number of records processed
        """
        cursor = self.db.get_cursor()
        
        try:
            # Get batch of records
            cursor.execute("""
                SELECT id, json_data, issuer_city, issuer_state, issuer_zip,
                       offering_amount, minimum_investment, total_amount_sold, total_remaining
                FROM form_d_filings
                WHERE id > %s
                  AND (issuer_city IS NULL OR issuer_state IS NULL OR issuer_zip IS NULL
                       OR offering_amount IS NULL OR minimum_investment IS NULL
                       OR total_amount_sold IS NULL OR total_remaining IS NULL)
                ORDER BY id
                LIMIT %s
            """, (self.progress['last_id'], batch_size))
            
            records = cursor.fetchall()
            if not records:
                return 0
            
            # Process records in batch
            batch_fixes = {
                'issuer_city': 0, 'issuer_state': 0, 'issuer_zip': 0,
                'offering_amount': 0, 'minimum_investment': 0,
                'total_amount_sold': 0, 'total_remaining': 0
            }
            
            for record in records:
                record_id = record['id']
                json_data = record['json_data']
                
                # Extract all data in single pass
                extracted = self.extract_all_data_from_json(json_data)
                
                # Build update statement
                update_fields = []
                update_values = []
                
                field_mappings = {
                    'issuer_city': 'issuer_city',
                    'issuer_state': 'issuer_state', 
                    'issuer_zip': 'issuer_zip',
                    'offering_amount': 'offering_amount',
                    'minimum_investment': 'minimum_investment',
                    'total_amount_sold': 'total_amount_sold',
                    'total_remaining': 'total_remaining'
                }
                
                for db_field, extract_field in field_mappings.items():
                    if record[db_field] is None and extracted[extract_field] is not None:
                        update_fields.append(f"{db_field} = %s")
                        update_values.append(extracted[extract_field])
                        batch_fixes[db_field] += 1
                
                # Execute update if we have changes
                if update_fields:
                    update_values.append(record_id)
                    cursor.execute(f"""
                        UPDATE form_d_filings
                        SET {', '.join(update_fields)}, updated_at = NOW()
                        WHERE id = %s
                    """, update_values)
            
            # Commit batch
            self.db.commit()
            
            # Update progress
            self.progress['processed'] += len(records)
            self.progress['last_id'] = max(record['id'] for record in records)
            for field, count in batch_fixes.items():
                self.progress['fixed_counts'][field] += count
            
            logger.info(f"✅ Processed {len(records)} records, fixed {sum(batch_fixes.values())} fields")
            
            return len(records)
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ Batch processing failed: {e}")
            raise
    
    def run_simple_fix(self, batch_size: int = 2000) -> Dict[str, Any]:
        """
        Run the simple high-performance fix.
        
        Args:
            batch_size: Number of records per batch
            
        Returns:
            Summary of the fix operation
        """
        start_time = datetime.now()
        if not self.progress['start_time']:
            self.progress['start_time'] = start_time.isoformat()
        
        logger.info("🚀 Starting simple high-performance NULL value fix...")
        
        # Get total records to process
        cursor = self.db.get_cursor()
        cursor.execute("""
            SELECT COUNT(*) as total
            FROM form_d_filings
            WHERE id > %s
              AND (issuer_city IS NULL OR issuer_state IS NULL OR issuer_zip IS NULL
                   OR offering_amount IS NULL OR minimum_investment IS NULL
                   OR total_amount_sold IS NULL OR total_remaining IS NULL)
        """, (self.progress['last_id'],))
        
        total_to_process = cursor.fetchone()['total']
        logger.info(f"📊 Found {total_to_process:,} records to process (resuming from ID {self.progress['last_id']})")
        
        if total_to_process == 0:
            logger.info("✅ No records need processing")
            return self.create_summary(start_time)
        
        # Process in batches
        processed_this_run = 0
        
        while True:
            batch_start = time.time()
            records_processed = self.process_batch(batch_size)
            
            if records_processed == 0:
                break
            
            processed_this_run += records_processed
            
            # Save progress checkpoint
            self.save_progress()
            
            # Progress reporting
            elapsed = (datetime.now() - start_time).total_seconds()
            progress_pct = (processed_this_run / total_to_process) * 100 if total_to_process > 0 else 0
            records_per_sec = processed_this_run / elapsed if elapsed > 0 else 0
            eta_seconds = (total_to_process - processed_this_run) / records_per_sec if records_per_sec > 0 else 0
            
            logger.info(f"📈 Progress: {processed_this_run:,}/{total_to_process:,} "
                      f"({progress_pct:.1f}%) | Speed: {records_per_sec:.1f} rec/sec | "
                      f"ETA: {eta_seconds/60:.1f} min")
        
        return self.create_summary(start_time)
    
    def create_summary(self, start_time: datetime) -> Dict[str, Any]:
        """Create a summary of the fix operation."""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration,
            'duration_hours': duration / 3600,
            'records_processed': self.progress['processed'],
            'fixed_counts': self.progress['fixed_counts'],
            'total_fixes': sum(self.progress['fixed_counts'].values()),
            'records_per_second': self.progress['processed'] / duration if duration > 0 else 0
        }

def main():
    """Main function to run the simple high-performance NULL value fixes."""
    try:
        fixer = SimpleHighPerformanceNullFixer()
        summary = fixer.run_simple_fix(batch_size=2000)
        
        # Print summary
        print("\n" + "="*80)
        print("🚀 SIMPLE HIGH-PERFORMANCE DATABASE NULL VALUE FIX SUMMARY")
        print("="*80)
        print(f"⏱️  Duration: {summary['duration_hours']:.2f} hours ({summary['duration_seconds']:.1f} seconds)")
        print(f"📊 Records processed: {summary['records_processed']:,}")
        print(f"🔧 Total fixes: {summary['total_fixes']:,}")
        print(f"⚡ Speed: {summary['records_per_second']:.1f} records/second")
        print("\n📋 Fixes by field:")
        for field, count in summary['fixed_counts'].items():
            if count > 0:
                print(f"   • {field}: {count:,} records")
        print("="*80)
        
        # Save summary
        summary_file = f"simple_fix_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"📄 Summary saved to: {summary_file}")
        
    except Exception as e:
        logger.error(f"❌ Failed to run simple high-performance NULL value fixes: {e}")
        sys.exit(1)
    finally:
        if 'fixer' in locals():
            fixer.db.close()

if __name__ == "__main__":
    main()
