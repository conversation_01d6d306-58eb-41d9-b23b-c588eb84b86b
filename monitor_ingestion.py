#!/usr/bin/env python3
"""
Monitor Quarterly Data Ingestion Progress

Real-time monitoring of the ingestion process showing:
- Current filing count
- Processing rate
- Estimated completion time
- Top industries and offering amounts
"""

import sys
import time
from pathlib import Path
from datetime import datetime, timedelta

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from db.supabase_manager import SupabaseDatabaseManager

def monitor_progress():
    """Monitor the ingestion progress in real-time."""
    print("📊 Quarterly Data Ingestion Monitor")
    print("=" * 60)
    
    db = SupabaseDatabaseManager()
    
    # Track progress over time
    previous_count = 0
    start_time = datetime.now()
    
    try:
        while True:
            # Get current stats
            stats = db.get_stats()
            current_count = stats.get('form_d_filings_count', 0)
            
            # Calculate rate
            elapsed = (datetime.now() - start_time).total_seconds()
            if elapsed > 0:
                rate = (current_count - previous_count) / elapsed if elapsed > 60 else 0
            else:
                rate = 0
            
            # Clear screen and show progress
            print("\033[2J\033[H")  # Clear screen
            print("📊 QUARTERLY DATA INGESTION MONITOR")
            print("=" * 60)
            print(f"🕐 Time: {datetime.now().strftime('%H:%M:%S')}")
            print(f"📈 Total Filings: {current_count:,}")
            print(f"⚡ Processing Rate: {rate:.1f} filings/second")
            
            if rate > 0:
                # Estimate total filings (rough estimate based on existing data)
                estimated_total = 20000  # Approximate for all quarters
                remaining = max(0, estimated_total - current_count)
                eta_seconds = remaining / rate if rate > 0 else 0
                eta = datetime.now() + timedelta(seconds=eta_seconds)
                print(f"⏱️  ETA: {eta.strftime('%H:%M:%S')} ({eta_seconds/60:.0f} minutes)")
            
            # Show ZIP file processing status
            cursor = db.get_cursor()
            cursor.execute("""
                SELECT status, COUNT(*) as count 
                FROM zip_files 
                GROUP BY status 
                ORDER BY count DESC
            """)
            zip_status = cursor.fetchall()
            
            print(f"\n📦 ZIP File Status:")
            for status in zip_status:
                print(f"   {status['status']}: {status['count']} files")
            
            # Show recent activity
            cursor.execute("""
                SELECT COUNT(*) as recent_count
                FROM form_d_filings 
                WHERE created_at >= NOW() - INTERVAL '1 minute'
            """)
            recent = cursor.fetchone()
            print(f"\n🔥 Last minute: {recent['recent_count']} filings added")
            
            # Show top industries
            cursor.execute("""
                SELECT 
                    industry_group,
                    COUNT(*) as count,
                    SUM(offering_amount) as total_amount
                FROM form_d_filings 
                WHERE industry_group IS NOT NULL 
                  AND offering_amount IS NOT NULL
                GROUP BY industry_group
                ORDER BY total_amount DESC
                LIMIT 5
            """)
            industries = cursor.fetchall()
            
            print(f"\n🏭 Top Industries by Total Amount:")
            for ind in industries:
                if ind['total_amount']:
                    print(f"   {ind['industry_group']}: ${ind['total_amount']:,.0f} ({ind['count']} filings)")
            
            # Show largest recent filings
            cursor.execute("""
                SELECT 
                    issuer_name,
                    offering_amount,
                    industry_group,
                    filing_date
                FROM form_d_filings 
                WHERE offering_amount IS NOT NULL
                  AND created_at >= NOW() - INTERVAL '5 minutes'
                ORDER BY offering_amount DESC
                LIMIT 3
            """)
            large_filings = cursor.fetchall()
            
            if large_filings:
                print(f"\n💰 Largest Recent Filings:")
                for filing in large_filings:
                    amount = filing['offering_amount']
                    name = filing['issuer_name'][:40] + "..." if len(filing['issuer_name']) > 40 else filing['issuer_name']
                    industry = filing['industry_group'] or "Unknown"
                    print(f"   ${amount:,.0f} - {name} ({industry})")
            
            print(f"\n⏸️  Press Ctrl+C to stop monitoring")
            
            # Update for next iteration
            previous_count = current_count
            start_time = datetime.now()
            
            # Wait before next update
            time.sleep(10)
            
    except KeyboardInterrupt:
        print(f"\n\n📊 Final Stats:")
        final_stats = db.get_stats()
        print(f"   Total Filings: {final_stats.get('form_d_filings_count', 0):,}")
        print(f"   Total ZIP Files: {final_stats.get('zip_files_count', 0)}")
        if final_stats.get('total_amount'):
            print(f"   Total Offering Amount: ${final_stats['total_amount']:,.0f}")
        print(f"\n👋 Monitoring stopped.")
    
    finally:
        db.close()

if __name__ == "__main__":
    monitor_progress()
