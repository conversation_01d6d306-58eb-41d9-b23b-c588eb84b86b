#!/usr/bin/env python3
"""
Optimized Database NULL Value Fix Script

This is an optimized version that processes records more efficiently
and provides better progress tracking.
"""

import os
import sys
import json
import logging
import time
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from db.supabase_manager import SupabaseDatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OptimizedDatabaseNullFixer:
    """Optimized version of the database NULL value fixer."""
    
    def __init__(self):
        """Initialize the fixer with database connection."""
        try:
            self.db = SupabaseDatabaseManager()
            logger.info("✅ Connected to Supabase database")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            raise
    
    def extract_data_from_json(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract both issuer and offering data from JSON in one pass.
        
        Args:
            json_data: The full JSON data from the filing
            
        Returns:
            Dictionary with all extracted data
        """
        def safe_numeric(value):
            """Convert value to float, handling various formats."""
            if value is None or value == '' or str(value).lower() in ['indefinite', 'n/a', 'none', 'unlimited']:
                return None
            try:
                clean_value = str(value).replace(',', '').replace('$', '').replace(' ', '')
                return float(clean_value)
            except (ValueError, TypeError):
                return None
        
        extracted = {
            'issuer_city': None,
            'issuer_state': None,
            'issuer_zip': None,
            'offering_amount': None,
            'minimum_investment': None,
            'total_amount_sold': None,
            'total_remaining': None
        }
        
        # Extract issuer data
        issuer_paths = [
            ['issuers', 0],
            ['issuer'],
            ['primaryIssuer'],
            ['relatedPersons', 0]
        ]
        
        for path in issuer_paths:
            current = json_data
            try:
                for key in path:
                    if isinstance(key, int):
                        if isinstance(current, list) and len(current) > key:
                            current = current[key]
                        else:
                            break
                    else:
                        if isinstance(current, dict) and key in current:
                            current = current[key]
                        else:
                            break
                else:
                    if isinstance(current, dict):
                        # Extract city
                        for field in ['CITY', 'city', 'issuerCity']:
                            if field in current and current[field]:
                                extracted['issuer_city'] = str(current[field]).strip()
                                break
                        
                        # Extract state
                        for field in ['STATEORCOUNTRY', 'stateOrCountry', 'state', 'issuerState']:
                            if field in current and current[field]:
                                extracted['issuer_state'] = str(current[field]).strip()
                                break
                        
                        # Extract zip
                        for field in ['ZIPCODE', 'zipCode', 'zip', 'issuerZip']:
                            if field in current and current[field]:
                                extracted['issuer_zip'] = str(current[field]).strip()
                                break
                        
                        if any([extracted['issuer_city'], extracted['issuer_state'], extracted['issuer_zip']]):
                            break
            except (KeyError, IndexError, TypeError):
                continue
        
        # Extract offering data
        offering_paths = [
            ['offeringData'],
            ['offering'],
            ['']  # Top level
        ]
        
        for path in offering_paths:
            current = json_data
            try:
                if path and path[0]:
                    current = current[path[0]]
                
                if isinstance(current, dict):
                    # Extract offering amount
                    for field in ['TOTALOFFERINGAMOUNT', 'totalOfferingAmount', 'offeringAmount']:
                        if field in current:
                            extracted['offering_amount'] = safe_numeric(current[field])
                            if extracted['offering_amount'] is not None:
                                break
                    
                    # Extract minimum investment
                    for field in ['MINIMUMINVESTMENTACCEPTED', 'minimumInvestmentAccepted', 'minimumInvestment']:
                        if field in current:
                            extracted['minimum_investment'] = safe_numeric(current[field])
                            if extracted['minimum_investment'] is not None:
                                break
                    
                    # Extract total amount sold
                    for field in ['TOTALAMOUNTSOLD', 'totalAmountSold', 'amountSold']:
                        if field in current:
                            extracted['total_amount_sold'] = safe_numeric(current[field])
                            if extracted['total_amount_sold'] is not None:
                                break
                    
                    # Extract total remaining
                    for field in ['TOTALREMAINING', 'totalRemaining', 'remainingAmount']:
                        if field in current:
                            extracted['total_remaining'] = safe_numeric(current[field])
                            if extracted['total_remaining'] is not None:
                                break
                
                # If we found offering amount, we can stop looking
                if extracted['offering_amount'] is not None:
                    break
                    
            except (KeyError, TypeError):
                continue
        
        return extracted
    
    def fix_records_batch(self, batch_size: int = 500) -> Dict[str, int]:
        """
        Fix NULL values in batches with optimized processing.
        
        Args:
            batch_size: Number of records to process at once
            
        Returns:
            Dictionary with counts of fixed records
        """
        logger.info("🔧 Starting optimized NULL value fix...")
        
        cursor = self.db.get_cursor()
        fixed_counts = {
            'issuer_city': 0,
            'issuer_state': 0,
            'issuer_zip': 0,
            'offering_amount': 0,
            'minimum_investment': 0,
            'total_amount_sold': 0,
            'total_remaining': 0
        }
        
        try:
            # Get total count
            cursor.execute("""
                SELECT COUNT(*) as total
                FROM form_d_filings
                WHERE issuer_city IS NULL 
                   OR issuer_state IS NULL 
                   OR issuer_zip IS NULL
                   OR offering_amount IS NULL
                   OR minimum_investment IS NULL
                   OR total_amount_sold IS NULL
                   OR total_remaining IS NULL
            """)
            total_to_fix = cursor.fetchone()['total']
            logger.info(f"📊 Found {total_to_fix:,} records with NULL values to fix")
            
            if total_to_fix == 0:
                logger.info("✅ No records need fixing")
                return fixed_counts
            
            # Process in smaller batches for better performance
            offset = 0
            processed = 0
            start_time = time.time()
            
            while offset < total_to_fix:
                batch_start = time.time()
                
                # Get batch of records
                cursor.execute("""
                    SELECT id, json_data, issuer_city, issuer_state, issuer_zip,
                           offering_amount, minimum_investment, total_amount_sold, total_remaining
                    FROM form_d_filings
                    WHERE issuer_city IS NULL 
                       OR issuer_state IS NULL 
                       OR issuer_zip IS NULL
                       OR offering_amount IS NULL
                       OR minimum_investment IS NULL
                       OR total_amount_sold IS NULL
                       OR total_remaining IS NULL
                    ORDER BY id
                    LIMIT %s OFFSET %s
                """, (batch_size, offset))
                
                records = cursor.fetchall()
                if not records:
                    break
                
                # Process each record in the batch
                batch_updates = []
                for record in records:
                    record_id = record['id']
                    json_data = record['json_data']
                    
                    # Extract all data at once
                    extracted = self.extract_data_from_json(json_data)
                    
                    # Build update statement
                    update_fields = []
                    update_values = []
                    
                    # Check each field and update if NULL and we have data
                    field_mappings = {
                        'issuer_city': 'issuer_city',
                        'issuer_state': 'issuer_state', 
                        'issuer_zip': 'issuer_zip',
                        'offering_amount': 'offering_amount',
                        'minimum_investment': 'minimum_investment',
                        'total_amount_sold': 'total_amount_sold',
                        'total_remaining': 'total_remaining'
                    }
                    
                    for db_field, extract_field in field_mappings.items():
                        if record[db_field] is None and extracted[extract_field] is not None:
                            update_fields.append(f"{db_field} = %s")
                            update_values.append(extracted[extract_field])
                            fixed_counts[db_field] += 1
                    
                    # If we have updates, add to batch
                    if update_fields:
                        update_values.append(record_id)
                        batch_updates.append((update_fields, update_values))
                
                # Execute batch updates
                for update_fields, update_values in batch_updates:
                    cursor.execute(f"""
                        UPDATE form_d_filings
                        SET {', '.join(update_fields)}, updated_at = NOW()
                        WHERE id = %s
                    """, update_values)
                
                # Commit batch
                self.db.commit()
                
                processed += len(records)
                offset += batch_size
                
                # Progress reporting
                batch_time = time.time() - batch_start
                elapsed_time = time.time() - start_time
                progress_pct = (processed / total_to_fix) * 100
                
                if processed % (batch_size * 2) == 0 or processed >= total_to_fix:  # Report every 2 batches
                    records_per_sec = processed / elapsed_time if elapsed_time > 0 else 0
                    eta_seconds = (total_to_fix - processed) / records_per_sec if records_per_sec > 0 else 0
                    eta_minutes = eta_seconds / 60
                    
                    logger.info(f"📈 Processed {processed:,} / {total_to_fix:,} records ({progress_pct:.1f}%) "
                              f"| Speed: {records_per_sec:.1f} rec/sec | ETA: {eta_minutes:.1f} min")
            
            total_time = time.time() - start_time
            logger.info(f"✅ Completed fixing NULL values in {total_time:.1f} seconds")
            return fixed_counts
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ Failed to fix NULL values: {e}")
            raise
    
    def run_optimized_fix(self) -> Dict[str, Any]:
        """Run the optimized fix and return summary."""
        start_time = datetime.now()
        
        # Fix form_d_filings NULL values
        filing_fixes = self.fix_records_batch()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        summary = {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration,
            'form_d_filings_fixes': filing_fixes,
            'total_fixes': sum(filing_fixes.values())
        }
        
        logger.info(f"✅ Optimized fix completed in {duration:.1f} seconds")
        logger.info(f"📊 Total fixes applied: {summary['total_fixes']:,}")
        
        return summary

def main():
    """Main function to run the optimized NULL value fixes."""
    try:
        fixer = OptimizedDatabaseNullFixer()
        summary = fixer.run_optimized_fix()
        
        # Print summary
        print("\n" + "="*60)
        print("🔧 OPTIMIZED DATABASE NULL VALUE FIX SUMMARY")
        print("="*60)
        print(f"⏱️  Duration: {summary['duration_seconds']:.1f} seconds")
        print(f"📊 Total fixes: {summary['total_fixes']:,}")
        print("\n📋 Form D Filings Fixes:")
        for field, count in summary['form_d_filings_fixes'].items():
            if count > 0:
                print(f"   • {field}: {count:,} records")
        print("="*60)
        
        # Save summary
        summary_file = f"optimized_null_fix_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"📄 Summary saved to: {summary_file}")
        
    except Exception as e:
        logger.error(f"❌ Failed to run optimized NULL value fixes: {e}")
        sys.exit(1)
    finally:
        if 'fixer' in locals():
            fixer.db.close()

if __name__ == "__main__":
    main()
