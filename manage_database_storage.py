#!/usr/bin/env python3
"""
Database Storage Management Script

This script manages Supabase database storage within the 0.5GB limit by implementing
a tiered archival strategy and automated storage monitoring.
"""

import os
import sys
import json
import gzip
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from db.supabase_manager import SupabaseDatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseStorageManager:
    """Manages database storage within Supabase limits."""
    
    def __init__(self):
        """Initialize the storage manager."""
        try:
            self.db = SupabaseDatabaseManager()
            self.archive_dir = Path("data/archived")
            self.cold_storage_dir = Path("data/cold_storage")
            
            # Create archive directories
            self.archive_dir.mkdir(parents=True, exist_ok=True)
            self.cold_storage_dir.mkdir(parents=True, exist_ok=True)
            
            # Storage limits (in bytes)
            self.max_storage_bytes = 0.5 * 1024 * 1024 * 1024  # 0.5GB
            self.warning_threshold = 0.45 * 1024 * 1024 * 1024  # 0.45GB (90%)
            self.target_storage = 0.4 * 1024 * 1024 * 1024  # 0.4GB (80%)
            
            logger.info("✅ Storage manager initialized")
        except Exception as e:
            logger.error(f"❌ Failed to initialize storage manager: {e}")
            raise
    
    def get_database_size(self) -> Dict[str, Any]:
        """Get current database size and table statistics."""
        cursor = self.db.get_cursor()
        
        try:
            # Get database size
            cursor.execute("""
                SELECT 
                    pg_size_pretty(pg_database_size(current_database())) as size_pretty,
                    pg_database_size(current_database()) as size_bytes
            """)
            db_size = cursor.fetchone()
            
            # Get table sizes
            cursor.execute("""
                SELECT 
                    schemaname,
                    tablename,
                    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size_pretty,
                    pg_total_relation_size(schemaname||'.'||tablename) as size_bytes,
                    pg_stat_get_tuples_returned(c.oid) as rows_read,
                    pg_stat_get_tuples_inserted(c.oid) as rows_inserted
                FROM pg_tables pt
                JOIN pg_class c ON c.relname = pt.tablename
                WHERE schemaname = 'public'
                ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
            """)
            table_sizes = cursor.fetchall()
            
            # Get record counts
            record_counts = {}
            for table in ['form_d_filings', 'zip_files', 'feed_entries', 'analysis_results']:
                cursor.execute(f"SELECT COUNT(*) as count FROM {table}")
                result = cursor.fetchone()
                record_counts[table] = result['count']
            
            return {
                'database_size_bytes': db_size['size_bytes'],
                'database_size_pretty': db_size['size_pretty'],
                'table_sizes': [dict(row) for row in table_sizes],
                'record_counts': record_counts,
                'usage_percentage': (db_size['size_bytes'] / self.max_storage_bytes) * 100,
                'warning_threshold_reached': db_size['size_bytes'] > self.warning_threshold,
                'max_threshold_reached': db_size['size_bytes'] > self.max_storage_bytes
            }
            
        except Exception as e:
            logger.error(f"❌ Failed to get database size: {e}")
            return {}
    
    def identify_archival_candidates(self, target_reduction_bytes: int) -> List[Dict[str, Any]]:
        """
        Identify records for archival based on age and data completeness.
        
        Args:
            target_reduction_bytes: Target amount of data to archive
            
        Returns:
            List of records to archive, prioritized by archival strategy
        """
        cursor = self.db.get_cursor()
        
        try:
            # Strategy 1: Archive filings older than 3 years with complete data
            three_years_ago = (datetime.now() - timedelta(days=3*365)).strftime('%Y-%m-%d')
            
            cursor.execute("""
                SELECT 
                    id, accession_number, filing_date, issuer_name,
                    CASE 
                        WHEN issuer_city IS NOT NULL AND issuer_state IS NOT NULL 
                             AND issuer_zip IS NOT NULL THEN 3
                        WHEN offering_amount IS NOT NULL THEN 2
                        ELSE 1
                    END as data_quality_score,
                    pg_column_size(json_data) as record_size_bytes
                FROM form_d_filings
                WHERE filing_date < %s
                ORDER BY data_quality_score DESC, filing_date ASC
            """, (three_years_ago,))
            
            old_complete_records = cursor.fetchall()
            
            # Strategy 2: Archive filings older than 5 years (regardless of completeness)
            five_years_ago = (datetime.now() - timedelta(days=5*365)).strftime('%Y-%m-%d')
            
            cursor.execute("""
                SELECT 
                    id, accession_number, filing_date, issuer_name,
                    1 as data_quality_score,
                    pg_column_size(json_data) as record_size_bytes
                FROM form_d_filings
                WHERE filing_date < %s
                  AND id NOT IN (
                      SELECT id FROM form_d_filings WHERE filing_date < %s
                  )
                ORDER BY filing_date ASC
            """, (five_years_ago, three_years_ago))
            
            very_old_records = cursor.fetchall()
            
            # Combine and prioritize
            candidates = []
            total_bytes = 0
            
            # First, add complete old records
            for record in old_complete_records:
                if total_bytes >= target_reduction_bytes:
                    break
                candidates.append({
                    'id': record['id'],
                    'accession_number': record['accession_number'],
                    'filing_date': record['filing_date'],
                    'issuer_name': record['issuer_name'],
                    'data_quality_score': record['data_quality_score'],
                    'record_size_bytes': record['record_size_bytes'],
                    'archival_tier': '3_year_complete'
                })
                total_bytes += record['record_size_bytes']
            
            # Then, add very old records if needed
            for record in very_old_records:
                if total_bytes >= target_reduction_bytes:
                    break
                candidates.append({
                    'id': record['id'],
                    'accession_number': record['accession_number'],
                    'filing_date': record['filing_date'],
                    'issuer_name': record['issuer_name'],
                    'data_quality_score': record['data_quality_score'],
                    'record_size_bytes': record['record_size_bytes'],
                    'archival_tier': '5_year_any'
                })
                total_bytes += record['record_size_bytes']
            
            logger.info(f"📋 Identified {len(candidates)} records for archival ({total_bytes:,} bytes)")
            return candidates
            
        except Exception as e:
            logger.error(f"❌ Failed to identify archival candidates: {e}")
            return []
    
    def archive_records(self, candidates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Archive records to compressed JSON files.
        
        Args:
            candidates: List of records to archive
            
        Returns:
            Summary of archival operation
        """
        cursor = self.db.get_cursor()
        archived_count = 0
        archived_bytes = 0
        archive_files = []
        
        try:
            # Group candidates by archival tier and date
            tiers = {}
            for candidate in candidates:
                tier = candidate['archival_tier']
                if tier not in tiers:
                    tiers[tier] = []
                tiers[tier].append(candidate)
            
            for tier, records in tiers.items():
                # Create archive file for this tier
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                archive_file = self.archive_dir / f"form_d_archive_{tier}_{timestamp}.json.gz"
                
                # Get full record data
                record_ids = [r['id'] for r in records]
                cursor.execute("""
                    SELECT * FROM form_d_filings
                    WHERE id = ANY(%s)
                """, (record_ids,))
                
                full_records = cursor.fetchall()
                
                # Create archive data structure
                archive_data = {
                    'archive_metadata': {
                        'created_at': datetime.now().isoformat(),
                        'tier': tier,
                        'record_count': len(full_records),
                        'original_table': 'form_d_filings'
                    },
                    'records': [dict(record) for record in full_records]
                }
                
                # Write compressed archive
                with gzip.open(archive_file, 'wt', encoding='utf-8') as f:
                    json.dump(archive_data, f, indent=2, default=str)
                
                # Verify archive integrity
                with gzip.open(archive_file, 'rt', encoding='utf-8') as f:
                    verification = json.load(f)
                    if len(verification['records']) != len(full_records):
                        raise ValueError(f"Archive verification failed for {archive_file}")
                
                # Delete records from database
                cursor.execute("""
                    DELETE FROM form_d_filings
                    WHERE id = ANY(%s)
                """, (record_ids,))
                
                self.db.commit()
                
                archived_count += len(records)
                archived_bytes += sum(r['record_size_bytes'] for r in records)
                archive_files.append(str(archive_file))
                
                logger.info(f"✅ Archived {len(records)} records to {archive_file.name}")
            
            return {
                'archived_count': archived_count,
                'archived_bytes': archived_bytes,
                'archive_files': archive_files,
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ Failed to archive records: {e}")
            raise
    
    def monitor_and_manage_storage(self) -> Dict[str, Any]:
        """
        Monitor database storage and trigger archival if needed.
        
        Returns:
            Summary of storage management operation
        """
        logger.info("📊 Monitoring database storage...")
        
        # Get current storage status
        storage_info = self.get_database_size()
        
        if not storage_info:
            return {'error': 'Failed to get storage information'}
        
        current_bytes = storage_info['database_size_bytes']
        usage_pct = storage_info['usage_percentage']
        
        logger.info(f"💾 Current usage: {storage_info['database_size_pretty']} ({usage_pct:.1f}%)")
        
        result = {
            'timestamp': datetime.now().isoformat(),
            'storage_info': storage_info,
            'action_taken': 'none',
            'archival_summary': None
        }
        
        # Check if archival is needed
        if current_bytes > self.warning_threshold:
            logger.warning(f"⚠️ Storage usage above warning threshold ({usage_pct:.1f}%)")
            
            # Calculate target reduction
            target_reduction = current_bytes - self.target_storage
            
            logger.info(f"🎯 Target reduction: {target_reduction:,} bytes")
            
            # Identify and archive records
            candidates = self.identify_archival_candidates(target_reduction)
            
            if candidates:
                archival_summary = self.archive_records(candidates)
                result['action_taken'] = 'archival'
                result['archival_summary'] = archival_summary
                
                # Get updated storage info
                updated_storage = self.get_database_size()
                result['storage_info_after'] = updated_storage
                
                logger.info(f"✅ Archival complete. New usage: {updated_storage['database_size_pretty']} "
                          f"({updated_storage['usage_percentage']:.1f}%)")
            else:
                logger.warning("⚠️ No suitable records found for archival")
                result['action_taken'] = 'no_candidates'
        else:
            logger.info(f"✅ Storage usage within limits ({usage_pct:.1f}%)")
        
        return result
    
    def restore_from_archive(self, archive_file: str) -> Dict[str, Any]:
        """
        Restore records from an archive file.
        
        Args:
            archive_file: Path to the archive file
            
        Returns:
            Summary of restoration operation
        """
        archive_path = Path(archive_file)
        if not archive_path.exists():
            raise FileNotFoundError(f"Archive file not found: {archive_file}")
        
        cursor = self.db.get_cursor()
        
        try:
            # Load archive data
            with gzip.open(archive_path, 'rt', encoding='utf-8') as f:
                archive_data = json.load(f)
            
            records = archive_data['records']
            restored_count = 0
            
            # Restore each record
            for record in records:
                # Remove id to allow auto-generation
                record_data = dict(record)
                record_id = record_data.pop('id')
                
                # Insert record
                columns = list(record_data.keys())
                values = list(record_data.values())
                placeholders = ', '.join(['%s'] * len(values))
                
                cursor.execute(f"""
                    INSERT INTO form_d_filings ({', '.join(columns)})
                    VALUES ({placeholders})
                    ON CONFLICT (accession_number) DO NOTHING
                """, values)
                
                restored_count += 1
            
            self.db.commit()
            
            logger.info(f"✅ Restored {restored_count} records from {archive_path.name}")
            
            return {
                'restored_count': restored_count,
                'archive_file': str(archive_path),
                'timestamp': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"❌ Failed to restore from archive: {e}")
            raise

def main():
    """Main function to run storage management."""
    try:
        manager = DatabaseStorageManager()
        
        # Run storage monitoring and management
        result = manager.monitor_and_manage_storage()
        
        # Print summary
        print("\n" + "="*80)
        print("💾 DATABASE STORAGE MANAGEMENT SUMMARY")
        print("="*80)
        print(f"📅 Timestamp: {result['timestamp']}")
        
        storage = result['storage_info']
        print(f"💾 Current usage: {storage['database_size_pretty']} ({storage['usage_percentage']:.1f}%)")
        print(f"🎯 Action taken: {result['action_taken']}")
        
        if result['archival_summary']:
            archival = result['archival_summary']
            print(f"📦 Archived: {archival['archived_count']:,} records ({archival['archived_bytes']:,} bytes)")
            print(f"📁 Archive files: {len(archival['archive_files'])}")
        
        if 'storage_info_after' in result:
            after = result['storage_info_after']
            print(f"💾 Usage after archival: {after['database_size_pretty']} ({after['usage_percentage']:.1f}%)")
        
        print("="*80)
        
        # Save detailed report
        report_file = f"storage_management_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(result, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved to: {report_file}")
        
    except Exception as e:
        logger.error(f"❌ Storage management failed: {e}")
        sys.exit(1)
    finally:
        if 'manager' in locals():
            manager.db.close()

if __name__ == "__main__":
    main()
