#!/usr/bin/env python3
"""
Test Database Improvements Script

This script tests all three database improvements:
1. High-performance NULL value fix
2. Enhanced data ingestion pipeline
3. Database storage management
"""

import os
import sys
import json
import logging
import time
from typing import Dict, List, Any
from datetime import datetime
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

from db.supabase_manager import SupabaseDatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseImprovementTester:
    """Tests all database improvements."""
    
    def __init__(self):
        """Initialize the tester."""
        try:
            self.db = SupabaseDatabaseManager()
            logger.info("✅ Connected to database for testing")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            raise
    
    def test_enhanced_extraction(self) -> Dict[str, Any]:
        """Test the enhanced JSON extraction methods."""
        logger.info("🧪 Testing enhanced JSON extraction...")
        
        # Sample test data with various JSON structures
        test_cases = [
            {
                'name': 'Standard Structure',
                'data': {
                    'accessionNumber': 'TEST-001',
                    'issuerName': 'Test Company Inc.',
                    'filingDate': '2024-01-15',
                    'issuers': [{
                        'CITY': 'New York',
                        'STATEORCOUNTRY': 'NY',
                        'ZIPCODE': '10001'
                    }],
                    'offeringData': {
                        'TOTALOFFERINGAMOUNT': '1000000',
                        'MINIMUMINVESTMENTACCEPTED': '50000',
                        'TOTALAMOUNTSOLD': '750000',
                        'TOTALREMAINING': '250000'
                    }
                }
            },
            {
                'name': 'Alternative Structure',
                'data': {
                    'accessionNumber': 'TEST-002',
                    'issuerName': 'Alternative Corp',
                    'filingDate': '2024-01-16',
                    'primaryIssuer': {
                        'city': 'Los Angeles',
                        'state': 'CA',
                        'zip': '90210'
                    },
                    'offering': {
                        'totalOfferingAmount': '2000000',
                        'minimumInvestment': '100000'
                    }
                }
            },
            {
                'name': 'Minimal Structure',
                'data': {
                    'accessionNumber': 'TEST-003',
                    'issuerName': 'Minimal LLC',
                    'filingDate': '2024-01-17',
                    'issuerCity': 'Chicago',
                    'issuerState': 'IL',
                    'offeringAmount': '500000'
                }
            }
        ]
        
        results = []
        
        for test_case in test_cases:
            logger.info(f"  Testing: {test_case['name']}")
            
            # Test enhanced extraction
            extracted = self.db.enhanced_extract_from_json(test_case['data'])
            
            # Count successful extractions
            issuer_fields = sum(1 for field in ['issuer_city', 'issuer_state', 'issuer_zip'] 
                              if extracted[field] is not None)
            offering_fields = sum(1 for field in ['offering_amount', 'minimum_investment', 
                                                'total_amount_sold', 'total_remaining'] 
                                if extracted[field] is not None)
            
            result = {
                'test_case': test_case['name'],
                'extracted_data': extracted,
                'issuer_fields_extracted': issuer_fields,
                'offering_fields_extracted': offering_fields,
                'total_fields_extracted': issuer_fields + offering_fields
            }
            
            results.append(result)
            logger.info(f"    ✅ Extracted {result['total_fields_extracted']} fields")
        
        return {
            'test_results': results,
            'summary': {
                'total_tests': len(test_cases),
                'avg_fields_extracted': sum(r['total_fields_extracted'] for r in results) / len(results)
            }
        }
    
    def test_storage_monitoring(self) -> Dict[str, Any]:
        """Test database storage monitoring."""
        logger.info("🧪 Testing storage monitoring...")
        
        try:
            from manage_database_storage import DatabaseStorageManager
            
            storage_manager = DatabaseStorageManager()
            storage_info = storage_manager.get_database_size()
            
            if storage_info:
                logger.info(f"  ✅ Storage monitoring working: {storage_info['database_size_pretty']}")
                return {
                    'status': 'success',
                    'storage_info': storage_info
                }
            else:
                logger.error("  ❌ Storage monitoring failed")
                return {'status': 'failed'}
                
        except Exception as e:
            logger.error(f"  ❌ Storage monitoring test failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def test_null_fix_performance(self, sample_size: int = 100) -> Dict[str, Any]:
        """Test NULL fix performance on a sample of records."""
        logger.info(f"🧪 Testing NULL fix performance on {sample_size} records...")
        
        cursor = self.db.get_cursor()
        
        try:
            # Get sample records with NULL values
            cursor.execute("""
                SELECT id, json_data, issuer_city, issuer_state, issuer_zip,
                       offering_amount, minimum_investment, total_amount_sold, total_remaining
                FROM form_d_filings
                WHERE issuer_city IS NULL OR issuer_state IS NULL OR issuer_zip IS NULL
                   OR offering_amount IS NULL OR minimum_investment IS NULL
                   OR total_amount_sold IS NULL OR total_remaining IS NULL
                ORDER BY id
                LIMIT %s
            """, (sample_size,))
            
            records = cursor.fetchall()
            
            if not records:
                logger.info("  ✅ No records with NULL values found")
                return {'status': 'no_nulls', 'records_tested': 0}
            
            # Test extraction performance
            start_time = time.time()
            successful_extractions = 0
            total_fields_extracted = 0
            
            for record in records:
                json_data = record['json_data']
                
                # Use the enhanced extraction method
                extracted = self.db.enhanced_extract_from_json(json_data)
                
                # Count successful extractions
                fields_extracted = 0
                field_mappings = {
                    'issuer_city': 'issuer_city',
                    'issuer_state': 'issuer_state',
                    'issuer_zip': 'issuer_zip',
                    'offering_amount': 'offering_amount',
                    'minimum_investment': 'minimum_investment',
                    'total_amount_sold': 'total_amount_sold',
                    'total_remaining': 'total_remaining'
                }
                
                for db_field, extract_field in field_mappings.items():
                    if record[db_field] is None and extracted[extract_field] is not None:
                        fields_extracted += 1
                
                if fields_extracted > 0:
                    successful_extractions += 1
                    total_fields_extracted += fields_extracted
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            return {
                'status': 'success',
                'records_tested': len(records),
                'successful_extractions': successful_extractions,
                'total_fields_extracted': total_fields_extracted,
                'processing_time_seconds': processing_time,
                'records_per_second': len(records) / processing_time if processing_time > 0 else 0,
                'success_rate_percent': (successful_extractions / len(records)) * 100,
                'avg_fields_per_record': total_fields_extracted / len(records)
            }
            
        except Exception as e:
            logger.error(f"  ❌ NULL fix performance test failed: {e}")
            return {'status': 'error', 'error': str(e)}
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """Run all tests and generate a comprehensive report."""
        logger.info("🚀 Running comprehensive database improvement tests...")
        
        start_time = datetime.now()
        
        # Test 1: Enhanced extraction
        extraction_test = self.test_enhanced_extraction()
        
        # Test 2: Storage monitoring
        storage_test = self.test_storage_monitoring()
        
        # Test 3: NULL fix performance
        performance_test = self.test_null_fix_performance()
        
        end_time = datetime.now()
        
        return {
            'timestamp': start_time.isoformat(),
            'duration_seconds': (end_time - start_time).total_seconds(),
            'tests': {
                'enhanced_extraction': extraction_test,
                'storage_monitoring': storage_test,
                'null_fix_performance': performance_test
            },
            'overall_status': self.determine_overall_status([
                extraction_test, storage_test, performance_test
            ])
        }
    
    def determine_overall_status(self, test_results: List[Dict[str, Any]]) -> str:
        """Determine overall test status."""
        statuses = []
        
        for test in test_results:
            if 'status' in test:
                statuses.append(test['status'])
            elif 'test_results' in test:  # extraction test
                statuses.append('success')
        
        if all(status == 'success' for status in statuses):
            return 'all_passed'
        elif any(status == 'error' for status in statuses):
            return 'errors_found'
        else:
            return 'partial_success'
    
    def print_test_report(self, report: Dict[str, Any]) -> None:
        """Print a formatted test report."""
        print("\n" + "="*80)
        print("🧪 DATABASE IMPROVEMENTS TEST REPORT")
        print("="*80)
        print(f"📅 Timestamp: {report['timestamp']}")
        print(f"⏱️  Duration: {report['duration_seconds']:.2f} seconds")
        print(f"🎯 Overall Status: {report['overall_status'].upper()}")
        print()
        
        # Enhanced extraction test
        extraction = report['tests']['enhanced_extraction']
        print("🔍 ENHANCED EXTRACTION TEST:")
        print(f"   Tests run: {extraction['summary']['total_tests']}")
        print(f"   Avg fields extracted: {extraction['summary']['avg_fields_extracted']:.1f}")
        for result in extraction['test_results']:
            print(f"   • {result['test_case']}: {result['total_fields_extracted']} fields")
        print()
        
        # Storage monitoring test
        storage = report['tests']['storage_monitoring']
        print("💾 STORAGE MONITORING TEST:")
        if storage['status'] == 'success':
            info = storage['storage_info']
            print(f"   ✅ Status: Working")
            print(f"   💾 Current usage: {info['database_size_pretty']} ({info['usage_percentage']:.1f}%)")
        else:
            print(f"   ❌ Status: {storage['status']}")
        print()
        
        # Performance test
        performance = report['tests']['null_fix_performance']
        print("⚡ NULL FIX PERFORMANCE TEST:")
        if performance['status'] == 'success':
            print(f"   ✅ Records tested: {performance['records_tested']:,}")
            print(f"   🎯 Success rate: {performance['success_rate_percent']:.1f}%")
            print(f"   ⚡ Speed: {performance['records_per_second']:.1f} records/sec")
            print(f"   📊 Avg fields per record: {performance['avg_fields_per_record']:.1f}")
        else:
            print(f"   Status: {performance['status']}")
        print()
        
        print("="*80)

def main():
    """Main function to run the comprehensive tests."""
    try:
        tester = DatabaseImprovementTester()
        report = tester.run_comprehensive_test()
        tester.print_test_report(report)
        
        # Save detailed report
        report_file = f"database_improvements_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved to: {report_file}")
        
        # Return appropriate exit code
        if report['overall_status'] == 'all_passed':
            sys.exit(0)
        else:
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        sys.exit(1)
    finally:
        if 'tester' in locals():
            tester.db.close()

if __name__ == "__main__":
    main()
