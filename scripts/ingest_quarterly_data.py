#!/usr/bin/env python3
"""
Quarterly ZIP Data Ingestion Script

Ingests all existing quarterly ZIP files into the Supabase database:
1. Processes ZIP files from 2020-2025
2. Extracts Form D JSON files
3. Stores metadata and filing data
4. Tracks processing status
"""

import os
import sys
import json
import zipfile
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import re

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from db.supabase_manager import SupabaseDatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QuarterlyDataIngester:
    """Handles ingestion of quarterly ZIP files into Supabase."""
    
    def __init__(self, data_dir: str = "data/raw"):
        """
        Initialize the ingester.
        
        Args:
            data_dir: Directory containing the quarterly ZIP files
        """
        self.data_dir = Path(data_dir)
        self.db = SupabaseDatabaseManager()
        self.stats = {
            'zip_files_processed': 0,
            'zip_files_skipped': 0,
            'filings_added': 0,
            'filings_updated': 0,
            'errors': 0
        }
    
    def find_quarterly_zips(self) -> List[Path]:
        """Find all quarterly ZIP files in the data directory."""
        zip_files = []
        
        # Pattern for quarterly ZIP files: YYYYqN_d.zip
        pattern = re.compile(r'(\d{4})q([1-4])_d\.zip$')
        
        for zip_path in self.data_dir.glob('**/*.zip'):
            if pattern.match(zip_path.name):
                zip_files.append(zip_path)
        
        # Sort by year and quarter
        zip_files.sort(key=lambda p: (
            int(re.search(r'(\d{4})q([1-4])', p.name).group(1)),  # year
            int(re.search(r'(\d{4})q([1-4])', p.name).group(2))   # quarter
        ))
        
        return zip_files
    
    def extract_date_from_filename(self, filename: str) -> str:
        """Extract date string from quarterly ZIP filename."""
        match = re.search(r'(\d{4})q([1-4])', filename)
        if match:
            year = int(match.group(1))
            quarter = int(match.group(2))
            
            # Convert quarter to approximate date
            quarter_dates = {1: '0331', 2: '0630', 3: '0930', 4: '1231'}
            return f"{year}{quarter_dates[quarter]}"
        
        return datetime.now().strftime('%Y%m%d')
    
    def find_jsonl_file(self, zip_path: Path) -> Optional[Path]:
        """Find the corresponding JSONL file for a ZIP file."""
        # Extract quarter info from ZIP filename
        match = re.search(r'(\d{4})q([1-4])', zip_path.name)
        if not match:
            return None

        year, quarter = match.groups()

        # The JSONL file is typically in a subdirectory: data/raw/YYYYqN/formd_YYYYqN.jsonl
        quarter_dir = zip_path.parent / f"{year}q{quarter}"
        jsonl_file = quarter_dir / f"formd_{year}q{quarter}.jsonl"

        if jsonl_file.exists():
            return jsonl_file

        # Fallback: try in the same directory as the ZIP file
        alt_jsonl = zip_path.parent / f"formd_{year}q{quarter}.jsonl"
        if alt_jsonl.exists():
            return alt_jsonl

        # Another fallback: try in the extracted directory
        extracted_dir = zip_path.parent / f"{year}Q{quarter}_d"
        alt_jsonl2 = extracted_dir / f"formd_{year}q{quarter}.jsonl"
        if alt_jsonl2.exists():
            return alt_jsonl2

        return None

    def process_zip_file(self, zip_path: Path) -> bool:
        """
        Process a single quarterly ZIP file by loading its corresponding JSONL file.

        Args:
            zip_path: Path to the ZIP file

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"📦 Processing ZIP file: {zip_path.name}")

            # Check if already processed
            existing_zip = self.db.get_zip_file(zip_path.name)
            if existing_zip and existing_zip['status'] == 'processed':
                logger.info(f"⏭️ Skipping already processed file: {zip_path.name}")
                self.stats['zip_files_skipped'] += 1
                return True

            # Add/update ZIP file record
            file_size = zip_path.stat().st_size
            date_str = self.extract_date_from_filename(zip_path.name)

            zip_id = self.db.add_zip_file(
                filename=zip_path.name,
                url=f"https://www.sec.gov/dera/data/form-d/{zip_path.name}",
                date_str=date_str,
                file_size=file_size,
                status='processing'
            )

            # Find corresponding JSONL file
            jsonl_file = self.find_jsonl_file(zip_path)
            if not jsonl_file:
                logger.warning(f"⚠️ No JSONL file found for {zip_path.name}")
                # Update status to indicate no data
                cursor = self.db.get_cursor()
                cursor.execute("""
                    UPDATE zip_files
                    SET status = 'no_data', error_message = 'No JSONL file found'
                    WHERE id = %s
                """, (zip_id,))
                self.db.commit()
                return True

            logger.info(f"📄 Found JSONL file: {jsonl_file.name}")

            # Process JSONL file
            filings_processed = 0

            try:
                with open(jsonl_file, 'r', encoding='utf-8') as f:
                    for line_num, line in enumerate(f, 1):
                        line = line.strip()
                        if not line:
                            continue

                        try:
                            filing_data = json.loads(line)

                            # Add to database
                            filing_id = self.db.add_form_d_filing(
                                filing_data=filing_data,
                                source_zip_id=zip_id,
                                source_file=f"{jsonl_file.name}:line_{line_num}"
                            )

                            if filing_id:
                                filings_processed += 1
                                if filings_processed % 100 == 0:
                                    logger.info(f"   📊 Processed {filings_processed} filings...")

                        except json.JSONDecodeError as e:
                            logger.warning(f"⚠️ Invalid JSON on line {line_num}: {e}")
                            continue
                        except Exception as e:
                            logger.warning(f"⚠️ Failed to process line {line_num}: {e}")
                            continue

            except Exception as e:
                logger.error(f"❌ Failed to read JSONL file {jsonl_file}: {e}")
                raise

            # Update ZIP file status
            cursor = self.db.get_cursor()
            cursor.execute("""
                UPDATE zip_files
                SET status = 'processed', last_accessed = NOW()
                WHERE id = %s
            """, (zip_id,))
            self.db.commit()

            logger.info(f"✅ Completed {zip_path.name}: {filings_processed} filings processed")
            self.stats['zip_files_processed'] += 1
            self.stats['filings_added'] += filings_processed

            return True

        except Exception as e:
            logger.error(f"❌ Failed to process {zip_path.name}: {e}")
            self.stats['errors'] += 1

            # Update ZIP file status to error
            try:
                cursor = self.db.get_cursor()
                cursor.execute("""
                    UPDATE zip_files
                    SET status = 'error', error_message = %s
                    WHERE filename = %s
                """, (str(e), zip_path.name))
                self.db.commit()
            except:
                pass

            return False
    
    def ingest_all(self) -> Dict[str, Any]:
        """
        Ingest all quarterly ZIP files.
        
        Returns:
            Dictionary with ingestion statistics
        """
        logger.info("🚀 Starting quarterly data ingestion")
        
        zip_files = self.find_quarterly_zips()
        logger.info(f"📦 Found {len(zip_files)} quarterly ZIP files")
        
        for i, zip_path in enumerate(zip_files, 1):
            logger.info(f"📦 Processing {i}/{len(zip_files)}: {zip_path.name}")
            self.process_zip_file(zip_path)
        
        # Get final statistics
        final_stats = self.db.get_stats()
        self.stats.update(final_stats)
        
        logger.info("🎉 Quarterly data ingestion completed")
        return self.stats
    
    def close(self):
        """Close database connection."""
        self.db.close()

def main():
    """Main ingestion function."""
    print("🚀 Quarterly ZIP Data Ingestion")
    print("=" * 50)
    
    ingester = QuarterlyDataIngester()
    
    try:
        stats = ingester.ingest_all()
        
        print("\n📊 INGESTION SUMMARY")
        print("=" * 50)
        print(f"ZIP files processed: {stats['zip_files_processed']}")
        print(f"ZIP files skipped: {stats['zip_files_skipped']}")
        print(f"Form D filings added: {stats['filings_added']}")
        print(f"Errors encountered: {stats['errors']}")
        print(f"Total filings in database: {stats.get('form_d_filings_count', 0)}")
        print(f"Total ZIP files in database: {stats.get('zip_files_count', 0)}")
        
        if stats.get('total_amount'):
            print(f"Total offering amount: ${stats['total_amount']:,.2f}")
            print(f"Average offering amount: ${stats['avg_amount']:,.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Ingestion failed: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        ingester.close()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
