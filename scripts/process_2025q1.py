#!/usr/bin/env python3
"""
2025Q1 Data Processing Script

Specifically processes the missing 2025Q1 JSONL file with improved connection handling
to avoid the timeout issues encountered during the overnight ingestion.
"""

import os
import sys
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from db.supabase_manager import SupabaseDatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class Q2025Processor:
    """Process 2025Q1 JSONL data with robust connection handling."""
    
    def __init__(self):
        """Initialize the processor."""
        self.db = None
        self.stats = {
            'filings_processed': 0,
            'filings_added': 0,
            'filings_skipped': 0,
            'errors': 0,
            'connection_resets': 0
        }
        
    def connect_db(self):
        """Establish database connection with retry logic."""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if self.db:
                    self.db.close()
                self.db = SupabaseDatabaseManager()
                logger.info("✅ Database connection established")
                return True
            except Exception as e:
                logger.warning(f"⚠️ Connection attempt {attempt + 1} failed: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # Exponential backoff
                else:
                    logger.error("❌ Failed to establish database connection")
                    return False
        return False
    
    def reset_connection(self):
        """Reset database connection."""
        logger.info("🔄 Resetting database connection...")
        self.stats['connection_resets'] += 1
        return self.connect_db()
    
    def find_2025q1_jsonl(self) -> Optional[Path]:
        """Find the 2025Q1 JSONL file."""
        data_dir = Path("data/raw")
        
        # Look for 2025Q1 JSONL file
        possible_paths = [
            data_dir / "2025q1" / "formd_2025q1.jsonl",
            data_dir / "formd_2025q1.jsonl",
        ]
        
        for path in possible_paths:
            if path.exists():
                logger.info(f"📄 Found 2025Q1 JSONL file: {path}")
                return path
        
        logger.error("❌ Could not find 2025Q1 JSONL file")
        return None
    
    def get_or_create_zip_record(self) -> Optional[int]:
        """Get or create the ZIP file record for 2025Q1."""
        try:
            # Check if 2025q1_d.zip already exists
            cursor = self.db.get_cursor()
            cursor.execute("SELECT id FROM zip_files WHERE filename = %s", ("2025q1_d.zip",))
            result = cursor.fetchone()
            
            if result:
                zip_id = result['id']
                logger.info(f"📦 Found existing ZIP record: 2025q1_d.zip (ID: {zip_id})")
                return zip_id
            else:
                # This shouldn't happen since we saw all 21 ZIP files, but just in case
                logger.warning("⚠️ 2025q1_d.zip record not found in database")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error checking ZIP record: {e}")
            return None
    
    def process_jsonl_file(self, jsonl_path: Path, zip_id: int) -> bool:
        """Process the 2025Q1 JSONL file with connection resilience."""
        logger.info(f"📄 Processing JSONL file: {jsonl_path}")
        
        # Count total lines first
        try:
            with open(jsonl_path, 'r', encoding='utf-8') as f:
                total_lines = sum(1 for line in f if line.strip())
            logger.info(f"📊 Total lines to process: {total_lines:,}")
        except Exception as e:
            logger.error(f"❌ Error counting lines: {e}")
            return False
        
        # Process file in batches
        batch_size = 100
        connection_reset_interval = 1000  # Reset connection every 1000 filings
        
        try:
            with open(jsonl_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                    
                    # Reset connection periodically to avoid timeouts
                    if line_num % connection_reset_interval == 0:
                        if not self.reset_connection():
                            logger.error("❌ Failed to reset connection, aborting")
                            return False
                    
                    try:
                        filing_data = json.loads(line)
                        
                        # Check if filing already exists
                        accession_number = filing_data.get('accessionNumber')
                        if accession_number:
                            cursor = self.db.get_cursor()
                            cursor.execute(
                                "SELECT id FROM form_d_filings WHERE accession_number = %s",
                                (accession_number,)
                            )
                            if cursor.fetchone():
                                self.stats['filings_skipped'] += 1
                                continue
                        
                        # Add to database
                        filing_id = self.db.add_form_d_filing(
                            filing_data=filing_data,
                            source_zip_id=zip_id,
                            source_file=f"{jsonl_path.name}:line_{line_num}"
                        )
                        
                        if filing_id:
                            self.stats['filings_added'] += 1
                        
                        self.stats['filings_processed'] += 1
                        
                        # Progress logging
                        if self.stats['filings_processed'] % batch_size == 0:
                            progress = (line_num / total_lines) * 100
                            logger.info(
                                f"   📊 Processed {self.stats['filings_processed']:,} filings "
                                f"({progress:.1f}% complete, {self.stats['filings_added']:,} added, "
                                f"{self.stats['filings_skipped']:,} skipped)"
                            )
                    
                    except json.JSONDecodeError as e:
                        logger.warning(f"⚠️ Invalid JSON on line {line_num}: {e}")
                        self.stats['errors'] += 1
                        continue
                    except Exception as e:
                        logger.warning(f"⚠️ Failed to process line {line_num}: {e}")
                        self.stats['errors'] += 1
                        
                        # If we get connection errors, try to reset
                        if "connection" in str(e).lower():
                            if not self.reset_connection():
                                logger.error("❌ Failed to reset connection after error, aborting")
                                return False
                        continue
            
            logger.info(f"✅ Successfully processed {jsonl_path.name}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error processing JSONL file: {e}")
            return False
    
    def run(self) -> bool:
        """Run the 2025Q1 processing."""
        logger.info("🚀 Starting 2025Q1 data processing")
        
        # Connect to database
        if not self.connect_db():
            return False
        
        try:
            # Find JSONL file
            jsonl_path = self.find_2025q1_jsonl()
            if not jsonl_path:
                return False
            
            # Get ZIP record
            zip_id = self.get_or_create_zip_record()
            if not zip_id:
                return False
            
            # Process the file
            success = self.process_jsonl_file(jsonl_path, zip_id)
            
            if success:
                logger.info("🎉 2025Q1 processing completed successfully")
                return True
            else:
                logger.error("❌ 2025Q1 processing failed")
                return False
                
        finally:
            if self.db:
                self.db.close()
    
    def print_summary(self):
        """Print processing summary."""
        print("\n📊 2025Q1 PROCESSING SUMMARY")
        print("=" * 50)
        print(f"Filings processed: {self.stats['filings_processed']:,}")
        print(f"Filings added: {self.stats['filings_added']:,}")
        print(f"Filings skipped: {self.stats['filings_skipped']:,}")
        print(f"Errors encountered: {self.stats['errors']:,}")
        print(f"Connection resets: {self.stats['connection_resets']:,}")

def main():
    """Main function."""
    print("🚀 2025Q1 Form D Data Processing")
    print("=" * 50)
    
    processor = Q2025Processor()
    
    try:
        success = processor.run()
        processor.print_summary()
        
        if success:
            print("\n✅ 2025Q1 processing completed successfully!")
            return True
        else:
            print("\n❌ 2025Q1 processing failed!")
            return False
            
    except KeyboardInterrupt:
        print("\n⚠️ Processing interrupted by user")
        processor.print_summary()
        return False
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        processor.print_summary()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
