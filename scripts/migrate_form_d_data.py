#!/usr/bin/env python3
"""
Form D Data Migration Script

This script re-processes all existing Form D filings in the database to extract
additional fields that were missed in the initial ingestion due to incomplete
parsing of the nested JSON structure.

The script will:
1. Read all existing filings from the database
2. Re-extract fields using enhanced parsing logic
3. Update records with the newly extracted data
4. Provide progress tracking and statistics
"""

import sys
import os
sys.path.append('.')

from db.supabase_manager import SupabaseDatabaseManager
import logging
from typing import Dict, Any, Optional
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FormDDataMigrator:
    """Migrates existing Form D data to extract missing fields."""
    
    def __init__(self):
        self.db_manager = SupabaseDatabaseManager()
        self.processed_count = 0
        self.updated_count = 0
        self.error_count = 0
        
    def safe_numeric(self, value) -> Optional[float]:
        """Safely convert value to numeric, handling edge cases."""
        if value is None or value == '' or str(value).lower() in ['indefinite', 'n/a', 'none']:
            return None
        try:
            # Remove commas and dollar signs, convert to float
            clean_value = str(value).replace(',', '').replace('$', '')
            return float(clean_value)
        except (ValueError, TypeError):
            return None
    
    def extract_enhanced_fields(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """Extract fields using enhanced parsing logic."""
        extracted = {}
        
        # Extract issuer address from nested structure
        issuer_city = None
        issuer_state = None
        issuer_zip = None
        file_number = None
        
        # Try to get issuer data from issuers array
        issuers = json_data.get('issuers', [])
        if issuers and len(issuers) > 0:
            issuer = issuers[0]  # Primary issuer
            issuer_city = issuer.get('CITY') or issuer.get('city')
            issuer_state = issuer.get('STATEORCOUNTRY') or issuer.get('stateOrCountry') or issuer.get('state')
            issuer_zip = issuer.get('ZIPCODE') or issuer.get('zipCode') or issuer.get('zip')
        
        # Extract offering data from nested structure
        offering_data = json_data.get('offeringData', {})
        
        offering_amount = self.safe_numeric(offering_data.get('TOTALOFFERINGAMOUNT'))
        minimum_investment = self.safe_numeric(offering_data.get('MINIMUMINVESTMENTACCEPTED'))
        total_amount_sold = self.safe_numeric(offering_data.get('TOTALAMOUNTSOLD'))
        total_remaining = self.safe_numeric(offering_data.get('TOTALREMAINING'))
        
        # Additional fields we can extract
        revenue_range = offering_data.get('REVENUERANGE', '')
        investment_fund_type = offering_data.get('INVESTMENTFUNDTYPE', '')
        federal_exemptions = offering_data.get('FEDERALEXEMPTIONS_ITEMS_LIST', '')
        
        # Determine offering type based on available flags
        offering_type = None
        if offering_data.get('ISEQUITYTYPE') == 'Y':
            offering_type = 'Equity'
        elif offering_data.get('ISDEBTTYPE') == 'Y':
            offering_type = 'Debt'
        elif offering_data.get('ISPOOLEDINVESTMENTFUNDTYPE') == 'Y':
            offering_type = 'Pooled Investment Fund'
        elif offering_data.get('ISOTHERTYPE') == 'Y':
            offering_type = 'Other'
        
        # Fallback to top-level fields if nested extraction fails
        if not offering_amount:
            offering_amount = self.safe_numeric(json_data.get('offeringAmount'))
        if not issuer_city:
            issuer_city = json_data.get('issuerCity')
        if not issuer_state:
            issuer_state = json_data.get('issuerState')
        if not issuer_zip:
            issuer_zip = json_data.get('issuerZip')
        if not offering_type:
            offering_type = json_data.get('offeringType')
        if not minimum_investment:
            minimum_investment = self.safe_numeric(json_data.get('minimumInvestment'))
        if not total_amount_sold:
            total_amount_sold = self.safe_numeric(json_data.get('totalAmountSold'))
        if not total_remaining:
            total_remaining = self.safe_numeric(json_data.get('totalRemaining'))
        
        return {
            'offering_amount': offering_amount,
            'issuer_city': issuer_city,
            'issuer_state': issuer_state,
            'issuer_zip': issuer_zip,
            'offering_type': offering_type,
            'minimum_investment': minimum_investment,
            'total_amount_sold': total_amount_sold,
            'total_remaining': total_remaining,
            'file_number': file_number
        }
    
    def update_filing_record(self, filing_id: int, extracted_fields: Dict[str, Any]) -> bool:
        """Update a filing record with extracted fields."""
        cursor = self.db_manager.get_cursor()
        try:
            cursor.execute("""
                UPDATE form_d_filings 
                SET 
                    offering_amount = COALESCE(%s, offering_amount),
                    issuer_city = COALESCE(%s, issuer_city),
                    issuer_state = COALESCE(%s, issuer_state),
                    issuer_zip = COALESCE(%s, issuer_zip),
                    offering_type = COALESCE(%s, offering_type),
                    minimum_investment = COALESCE(%s, minimum_investment),
                    total_amount_sold = COALESCE(%s, total_amount_sold),
                    total_remaining = COALESCE(%s, total_remaining),
                    file_number = COALESCE(%s, file_number),
                    updated_at = NOW()
                WHERE id = %s
            """, (
                extracted_fields['offering_amount'],
                extracted_fields['issuer_city'],
                extracted_fields['issuer_state'],
                extracted_fields['issuer_zip'],
                extracted_fields['offering_type'],
                extracted_fields['minimum_investment'],
                extracted_fields['total_amount_sold'],
                extracted_fields['total_remaining'],
                extracted_fields['file_number'],
                filing_id
            ))
            
            self.db_manager.commit()
            return True
            
        except Exception as e:
            self.db_manager.rollback()
            logger.error(f"Failed to update filing {filing_id}: {e}")
            return False
    
    def migrate_all_filings(self, batch_size: int = 1000):
        """Migrate all filings in batches."""
        cursor = self.db_manager.get_cursor()
        
        # Get total count
        cursor.execute("SELECT COUNT(*) as total FROM form_d_filings")
        total_count = cursor.fetchone()['total']
        
        logger.info(f"🚀 Starting migration of {total_count:,} Form D filings")
        logger.info(f"📦 Processing in batches of {batch_size:,}")
        
        offset = 0
        start_time = time.time()
        
        while offset < total_count:
            # Get batch of filings
            cursor.execute("""
                SELECT id, accession_number, json_data
                FROM form_d_filings 
                ORDER BY id
                LIMIT %s OFFSET %s
            """, (batch_size, offset))
            
            batch = cursor.fetchall()
            if not batch:
                break
                
            logger.info(f"📊 Processing batch {offset//batch_size + 1}: records {offset+1:,} to {offset+len(batch):,}")
            
            for record in batch:
                try:
                    filing_id = record['id']
                    accession_number = record['accession_number']
                    json_data = record['json_data']
                    
                    # Extract enhanced fields
                    extracted_fields = self.extract_enhanced_fields(json_data)
                    
                    # Update record
                    if self.update_filing_record(filing_id, extracted_fields):
                        self.updated_count += 1
                    
                    self.processed_count += 1
                    
                    # Progress update every 1000 records
                    if self.processed_count % 1000 == 0:
                        elapsed = time.time() - start_time
                        rate = self.processed_count / elapsed
                        remaining = (total_count - self.processed_count) / rate if rate > 0 else 0
                        logger.info(f"⚡ Progress: {self.processed_count:,}/{total_count:,} ({self.processed_count/total_count*100:.1f}%) - {rate:.1f} records/sec - ETA: {remaining/60:.1f} min")
                    
                except Exception as e:
                    self.error_count += 1
                    logger.error(f"Error processing filing {record.get('accession_number', 'unknown')}: {e}")
            
            offset += len(batch)
        
        # Final statistics
        elapsed = time.time() - start_time
        logger.info(f"✅ Migration completed!")
        logger.info(f"📊 Processed: {self.processed_count:,} records")
        logger.info(f"📊 Updated: {self.updated_count:,} records")
        logger.info(f"📊 Errors: {self.error_count:,} records")
        logger.info(f"⏱️ Total time: {elapsed/60:.1f} minutes")
        logger.info(f"⚡ Average rate: {self.processed_count/elapsed:.1f} records/second")

def main():
    """Main migration function."""
    try:
        migrator = FormDDataMigrator()
        migrator.migrate_all_filings(batch_size=1000)
        
    except KeyboardInterrupt:
        logger.info("🛑 Migration interrupted by user")
    except Exception as e:
        logger.error(f"❌ Migration failed: {e}")
        raise
    finally:
        # Clean up
        if 'migrator' in locals():
            migrator.db_manager.close()

if __name__ == "__main__":
    main()
