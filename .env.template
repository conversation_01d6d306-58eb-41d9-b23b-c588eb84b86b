# API Keys
SENDGRID_API_KEY=your_sendgrid_api_key
GITHUB_API_KEY=your_github_api_key

# Database Configuration (Supabase)
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# PostgreSQL Connection Options (try these in order)
# Option 1: Direct connection (may have IPv6 issues)
POSTGRES_CONNECTION_STRING=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require&connect_timeout=10

# Option 2: Connection pooling (often more IPv4-friendly)
POSTGRES_POOLER_CONNECTION_STRING=postgresql://postgres:<EMAIL>:5432/postgres?sslmode=require&connect_timeout=10

# Option 3: Session pooling (alternative)
POSTGRES_SESSION_CONNECTION_STRING=postgresql://postgres:<EMAIL>:6543/postgres?sslmode=require&connect_timeout=10

# Legacy SQLite (for migration reference)
SQLITE_DB_PATH=db/formd.db

# Email Configuration
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>

# SEC API Configuration
SEC_API_USER_AGENT="<NAME_EMAIL>"

# Model Configuration
MODEL_PATH=mlx-community/Mixtral-8x7B-Instruct-v0.1-4bit

# Microsoft 365 Configuration
MS365_CLIENT_ID=your_client_id
MS365_CLIENT_SECRET=your_client_secret
MS365_TENANT_ID=your_tenant_id

# Phase 3 Configuration
USE_MULTI_STAGE=true
USE_PROMPT_EVALUATION=true
USE_SPECIALIZED_ANALYSIS=true
SCREENING_THRESHOLD=0.3
RELEVANCE_THRESHOLD=0.7
EMAIL_THRESHOLD=0.8
