#!/usr/bin/env python3
"""
High-Performance Database NULL Value Fix Script

This script implements multi-threaded, high-performance batch processing
with connection pooling and checkpoint/resume functionality to complete
the NULL value fixes within 2-3 hours.
"""

import os
import sys
import json
import logging
import time
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
from pathlib import Path
import psycopg2
from psycopg2 import pool
from psycopg2.extras import RealDictCursor, <PERSON><PERSON>

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - [%(threadName)s] %(message)s'
)
logger = logging.getLogger(__name__)

class HighPerformanceNullFixer:
    """High-performance NULL value fixer with multi-threading and connection pooling."""
    
    def __init__(self, max_workers: int = 4):
        """
        Initialize the high-performance fixer.

        Args:
            max_workers: Maximum worker threads for parallel processing
        """
        self.max_workers = max_workers
        self.progress_file = "null_fix_progress.json"
        self.lock = threading.Lock()

        # Use existing Supabase manager instead of direct PostgreSQL connection
        try:
            from db.supabase_manager import SupabaseDatabaseManager
            self.db = SupabaseDatabaseManager()
            logger.info(f"✅ Connected to Supabase database")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            raise

        # Initialize progress tracking
        self.progress = self.load_progress()
        
    def load_progress(self) -> Dict[str, Any]:
        """Load progress from checkpoint file."""
        if os.path.exists(self.progress_file):
            try:
                with open(self.progress_file, 'r') as f:
                    progress = json.load(f)
                logger.info(f"📂 Loaded progress: {progress.get('processed', 0):,} records processed")
                return progress
            except Exception as e:
                logger.warning(f"⚠️ Failed to load progress file: {e}")
        
        return {
            'processed': 0,
            'last_id': 0,
            'start_time': None,
            'fixed_counts': {
                'issuer_city': 0, 'issuer_state': 0, 'issuer_zip': 0,
                'offering_amount': 0, 'minimum_investment': 0,
                'total_amount_sold': 0, 'total_remaining': 0
            }
        }
    
    def save_progress(self) -> None:
        """Save current progress to checkpoint file."""
        with self.lock:
            try:
                with open(self.progress_file, 'w') as f:
                    json.dump(self.progress, f, indent=2, default=str)
            except Exception as e:
                logger.error(f"❌ Failed to save progress: {e}")
    
    def get_connection(self):
        """Get a connection from the pool."""
        return self.connection_pool.getconn()
    
    def return_connection(self, conn):
        """Return a connection to the pool."""
        self.connection_pool.putconn(conn)
    
    def extract_all_data_from_json(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Single-pass extraction of all fields from JSON data.
        
        Args:
            json_data: The full JSON data from the filing
            
        Returns:
            Dictionary with all extracted data
        """
        def safe_numeric(value):
            """Convert value to float, handling various formats."""
            if value is None or value == '' or str(value).lower() in ['indefinite', 'n/a', 'none', 'unlimited']:
                return None
            try:
                clean_value = str(value).replace(',', '').replace('$', '').replace(' ', '')
                return float(clean_value)
            except (ValueError, TypeError):
                return None
        
        extracted = {
            'issuer_city': None, 'issuer_state': None, 'issuer_zip': None,
            'offering_amount': None, 'minimum_investment': None,
            'total_amount_sold': None, 'total_remaining': None
        }
        
        # Issuer data extraction paths (proven 100% success rate)
        issuer_paths = [
            ['issuers', 0], ['issuer'], ['primaryIssuer'], ['relatedPersons', 0]
        ]
        
        for path in issuer_paths:
            current = json_data
            try:
                for key in path:
                    if isinstance(key, int):
                        if isinstance(current, list) and len(current) > key:
                            current = current[key]
                        else:
                            break
                    else:
                        if isinstance(current, dict) and key in current:
                            current = current[key]
                        else:
                            break
                else:
                    if isinstance(current, dict):
                        # Extract issuer fields with all variations
                        city_fields = ['CITY', 'city', 'issuerCity', 'City']
                        state_fields = ['STATEORCOUNTRY', 'stateOrCountry', 'state', 'State', 'issuerState']
                        zip_fields = ['ZIPCODE', 'zipCode', 'zip', 'Zip', 'postalCode', 'issuerZip']
                        
                        for field in city_fields:
                            if field in current and current[field]:
                                extracted['issuer_city'] = str(current[field]).strip()
                                break
                        
                        for field in state_fields:
                            if field in current and current[field]:
                                extracted['issuer_state'] = str(current[field]).strip()
                                break
                        
                        for field in zip_fields:
                            if field in current and current[field]:
                                extracted['issuer_zip'] = str(current[field]).strip()
                                break
                        
                        if any([extracted['issuer_city'], extracted['issuer_state'], extracted['issuer_zip']]):
                            break
            except (KeyError, IndexError, TypeError):
                continue
        
        # Offering data extraction paths (50-93% success rate)
        offering_paths = [['offeringData'], ['offering'], ['']]  # '' means top level
        
        for path in offering_paths:
            current = json_data
            try:
                if path and path[0]:
                    current = current[path[0]]
                
                if isinstance(current, dict):
                    # Extract all offering fields
                    amount_fields = ['TOTALOFFERINGAMOUNT', 'totalOfferingAmount', 'offeringAmount']
                    min_inv_fields = ['MINIMUMINVESTMENTACCEPTED', 'minimumInvestmentAccepted', 'minimumInvestment']
                    sold_fields = ['TOTALAMOUNTSOLD', 'totalAmountSold', 'amountSold']
                    remaining_fields = ['TOTALREMAINING', 'totalRemaining', 'remainingAmount']
                    
                    if not extracted['offering_amount']:
                        for field in amount_fields:
                            if field in current:
                                extracted['offering_amount'] = safe_numeric(current[field])
                                if extracted['offering_amount'] is not None:
                                    break
                    
                    if not extracted['minimum_investment']:
                        for field in min_inv_fields:
                            if field in current:
                                extracted['minimum_investment'] = safe_numeric(current[field])
                                if extracted['minimum_investment'] is not None:
                                    break
                    
                    if not extracted['total_amount_sold']:
                        for field in sold_fields:
                            if field in current:
                                extracted['total_amount_sold'] = safe_numeric(current[field])
                                if extracted['total_amount_sold'] is not None:
                                    break
                    
                    if not extracted['total_remaining']:
                        for field in remaining_fields:
                            if field in current:
                                extracted['total_remaining'] = safe_numeric(current[field])
                                if extracted['total_remaining'] is not None:
                                    break
            except (KeyError, TypeError):
                continue
        
        return extracted
    
    def process_batch(self, batch_id: int, start_id: int, batch_size: int) -> Dict[str, int]:
        """
        Process a batch of records in a worker thread.
        
        Args:
            batch_id: Unique identifier for this batch
            start_id: Starting record ID for this batch
            batch_size: Number of records to process
            
        Returns:
            Dictionary with counts of fixed records
        """
        conn = None
        batch_fixes = {
            'issuer_city': 0, 'issuer_state': 0, 'issuer_zip': 0,
            'offering_amount': 0, 'minimum_investment': 0,
            'total_amount_sold': 0, 'total_remaining': 0
        }
        
        try:
            conn = self.get_connection()
            cursor = conn.cursor(cursor_factory=RealDictCursor)
            
            # Get batch of records
            cursor.execute("""
                SELECT id, json_data, issuer_city, issuer_state, issuer_zip,
                       offering_amount, minimum_investment, total_amount_sold, total_remaining
                FROM form_d_filings
                WHERE id > %s
                  AND (issuer_city IS NULL OR issuer_state IS NULL OR issuer_zip IS NULL
                       OR offering_amount IS NULL OR minimum_investment IS NULL
                       OR total_amount_sold IS NULL OR total_remaining IS NULL)
                ORDER BY id
                LIMIT %s
            """, (start_id, batch_size))
            
            records = cursor.fetchall()
            if not records:
                return batch_fixes
            
            # Process records in batch
            for record in records:
                record_id = record['id']
                json_data = record['json_data']
                
                # Extract all data in single pass
                extracted = self.extract_all_data_from_json(json_data)
                
                # Build update statement
                update_fields = []
                update_values = []
                
                field_mappings = {
                    'issuer_city': 'issuer_city',
                    'issuer_state': 'issuer_state', 
                    'issuer_zip': 'issuer_zip',
                    'offering_amount': 'offering_amount',
                    'minimum_investment': 'minimum_investment',
                    'total_amount_sold': 'total_amount_sold',
                    'total_remaining': 'total_remaining'
                }
                
                for db_field, extract_field in field_mappings.items():
                    if record[db_field] is None and extracted[extract_field] is not None:
                        update_fields.append(f"{db_field} = %s")
                        update_values.append(extracted[extract_field])
                        batch_fixes[db_field] += 1
                
                # Execute update if we have changes
                if update_fields:
                    update_values.append(record_id)
                    cursor.execute(f"""
                        UPDATE form_d_filings
                        SET {', '.join(update_fields)}, updated_at = NOW()
                        WHERE id = %s
                    """, update_values)
            
            # Commit batch
            conn.commit()
            
            # Update progress
            with self.lock:
                self.progress['processed'] += len(records)
                self.progress['last_id'] = max(record['id'] for record in records)
                for field, count in batch_fixes.items():
                    self.progress['fixed_counts'][field] += count
            
            logger.info(f"✅ Batch {batch_id}: Processed {len(records)} records, "
                       f"fixed {sum(batch_fixes.values())} fields")
            
            return batch_fixes
            
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"❌ Batch {batch_id} failed: {e}")
            return batch_fixes
        finally:
            if conn:
                self.return_connection(conn)
    
    def get_total_records_to_process(self) -> int:
        """Get total number of records that need processing."""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*) as total
                FROM form_d_filings
                WHERE id > %s
                  AND (issuer_city IS NULL OR issuer_state IS NULL OR issuer_zip IS NULL
                       OR offering_amount IS NULL OR minimum_investment IS NULL
                       OR total_amount_sold IS NULL OR total_remaining IS NULL)
            """, (self.progress['last_id'],))
            
            result = cursor.fetchone()
            return result[0]
        finally:
            self.return_connection(conn)
    
    def run_high_performance_fix(self, batch_size: int = 4000) -> Dict[str, Any]:
        """
        Run the high-performance fix with multi-threading.
        
        Args:
            batch_size: Number of records per batch
            
        Returns:
            Summary of the fix operation
        """
        start_time = datetime.now()
        if not self.progress['start_time']:
            self.progress['start_time'] = start_time.isoformat()
        
        logger.info("🚀 Starting high-performance NULL value fix...")
        
        # Get total records to process
        total_to_process = self.get_total_records_to_process()
        logger.info(f"📊 Found {total_to_process:,} records to process (resuming from ID {self.progress['last_id']})")
        
        if total_to_process == 0:
            logger.info("✅ No records need processing")
            return self.create_summary(start_time)
        
        # Process in parallel batches
        batch_id = 0
        current_id = self.progress['last_id']
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            futures = []
            
            while current_id < total_to_process + self.progress['last_id']:
                future = executor.submit(self.process_batch, batch_id, current_id, batch_size)
                futures.append(future)
                
                current_id += batch_size
                batch_id += 1
                
                # Limit concurrent batches to prevent overwhelming the database
                if len(futures) >= self.max_workers * 2:
                    # Wait for some batches to complete
                    completed = []
                    for future in as_completed(futures[:self.max_workers]):
                        completed.append(future)
                        future.result()  # Get result to handle any exceptions
                    
                    # Remove completed futures
                    for future in completed:
                        futures.remove(future)
                    
                    # Save progress checkpoint
                    self.save_progress()
                    
                    # Progress reporting
                    elapsed = (datetime.now() - start_time).total_seconds()
                    progress_pct = (self.progress['processed'] / total_to_process) * 100 if total_to_process > 0 else 0
                    records_per_sec = self.progress['processed'] / elapsed if elapsed > 0 else 0
                    eta_seconds = (total_to_process - self.progress['processed']) / records_per_sec if records_per_sec > 0 else 0
                    
                    logger.info(f"📈 Progress: {self.progress['processed']:,}/{total_to_process:,} "
                              f"({progress_pct:.1f}%) | Speed: {records_per_sec:.1f} rec/sec | "
                              f"ETA: {eta_seconds/60:.1f} min")
            
            # Wait for all remaining batches to complete
            for future in as_completed(futures):
                future.result()
        
        # Final save
        self.save_progress()
        
        return self.create_summary(start_time)
    
    def create_summary(self, start_time: datetime) -> Dict[str, Any]:
        """Create a summary of the fix operation."""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return {
            'start_time': start_time.isoformat(),
            'end_time': end_time.isoformat(),
            'duration_seconds': duration,
            'duration_hours': duration / 3600,
            'records_processed': self.progress['processed'],
            'fixed_counts': self.progress['fixed_counts'],
            'total_fixes': sum(self.progress['fixed_counts'].values()),
            'records_per_second': self.progress['processed'] / duration if duration > 0 else 0
        }
    
    def cleanup(self):
        """Clean up resources."""
        if hasattr(self, 'connection_pool'):
            self.connection_pool.closeall()
            logger.info("🔌 Closed all database connections")

def main():
    """Main function to run the high-performance NULL value fixes."""
    fixer = None
    try:
        fixer = HighPerformanceNullFixer(max_connections=8, max_workers=4)
        summary = fixer.run_high_performance_fix(batch_size=4000)
        
        # Print summary
        print("\n" + "="*80)
        print("🚀 HIGH-PERFORMANCE DATABASE NULL VALUE FIX SUMMARY")
        print("="*80)
        print(f"⏱️  Duration: {summary['duration_hours']:.2f} hours ({summary['duration_seconds']:.1f} seconds)")
        print(f"📊 Records processed: {summary['records_processed']:,}")
        print(f"🔧 Total fixes: {summary['total_fixes']:,}")
        print(f"⚡ Speed: {summary['records_per_second']:.1f} records/second")
        print("\n📋 Fixes by field:")
        for field, count in summary['fixed_counts'].items():
            if count > 0:
                print(f"   • {field}: {count:,} records")
        print("="*80)
        
        # Save summary
        summary_file = f"high_performance_fix_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(summary_file, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"📄 Summary saved to: {summary_file}")
        
    except Exception as e:
        logger.error(f"❌ Failed to run high-performance NULL value fixes: {e}")
        sys.exit(1)
    finally:
        if fixer:
            fixer.cleanup()

if __name__ == "__main__":
    main()
