
# This file was generated by 'versioneer.py' (0.18) from
# revision-control system data, or from the parent directory name of an
# unpacked source archive. Distribution tarballs contain a pre-generated copy
# of this file.

import json

version_json = '''
{
 "date": "2023-06-29T23:39:30+0200",
 "dirty": false,
 "error": null,
 "full-revisionid": "ab34b770aacac80431cd77f28770a60144679d38",
 "version": "0.4.15"
}
'''  # END VERSION_JSON


def get_versions():
    return json.loads(version_json)
