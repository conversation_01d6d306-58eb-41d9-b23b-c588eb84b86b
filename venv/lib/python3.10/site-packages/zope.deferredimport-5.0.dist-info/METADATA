Metadata-Version: 2.1
Name: zope.deferredimport
Version: 5.0
Summary: zope.deferredimport allows you to perform imports names that will only be resolved when used in the code.
Home-page: http://github.com/zopefoundation/zope.deferredimport
Author: Zope Foundation and Contributors
Author-email: <EMAIL>
License: ZPL 2.1
Project-URL: Issue Tracker, https://github.com/zopefoundation/zope.deferredimport/issues
Project-URL: Sources, https://github.com/zopefoundation/zope.deferredimport
Classifier: Development Status :: 5 - Production/Stable
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: Zope Public License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.7
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Programming Language :: Python :: Implementation :: PyPy
Classifier: Natural Language :: English
Classifier: Operating System :: OS Independent
Classifier: Topic :: Internet :: WWW/HTTP
Classifier: Topic :: Software Development
Requires-Python: >=3.7
License-File: LICENSE.txt
Requires-Dist: setuptools
Requires-Dist: zope.proxy
Provides-Extra: docs
Requires-Dist: Sphinx ; extra == 'docs'
Requires-Dist: repoze.sphinx.autointerface ; extra == 'docs'
Provides-Extra: test
Requires-Dist: zope.testrunner ; extra == 'test'

=========================
 ``zope.deferredimport``
=========================

.. image:: https://img.shields.io/pypi/v/zope.deferredimport.svg
        :target: https://pypi.python.org/pypi/zope.deferredimport/
        :alt: Latest release

.. image:: https://img.shields.io/pypi/pyversions/zope.deferredimport.svg
        :target: https://pypi.org/project/zope.deferredimport/
        :alt: Supported Python versions

.. image:: https://github.com/zopefoundation/zope.deferredimport/actions/workflows/tests.yml/badge.svg
        :target: https://github.com/zopefoundation/zope.deferredimport/actions/workflows/tests.yml

.. image:: https://coveralls.io/repos/github/zopefoundation/zope.deferredimport/badge.svg?branch=master
        :target: https://coveralls.io/github/zopefoundation/zope.deferredimport?branch=master

.. image:: https://readthedocs.org/projects/zopedeferredimport/badge/?version=latest
        :target: http://zopedeferredimport.readthedocs.io/en/latest/
        :alt: Documentation Status

Often, especially for package modules, you want to import names for
convenience, but not actually perform the imports until necessary.
The zope.deferredimport package provided facilities for defining names
in modules that will be imported from somewhere else when used.  You
can also cause deprecation warnings to be issued when a variable is
used.

Documentation is hosted at https://zopedeferredimport.readthedocs.io/


=========
 Changes
=========

5.0 (2023-06-29)
================

- Drop support for Python 2.7, 3.5, 3.6.

- Add support for Python 3.11.


4.4 (2021-12-10)
================

- Add support for Python 3.8, 3.9 and 3.10.

- Drop support for Python 3.4.


4.3.1 (2019-08-05)
==================

- Avoid race condition in ``deferredmodule.ModuleProxy.__getattr__``
  `#8 <https://github.com/zopefoundation/zope.deferredimport/issues/8>`_.


4.3 (2018-10-05)
================

- Add support for Python 3.7.


4.2.1 (2017-10-24)
==================

- Preserve the docstrings of proxied modules created with
  ``deprecatedFrom``, ``deferredFrom``, etc. See `issue 5
  <https://github.com/zopefoundation/zope.deferredimport/issues/5>`_.


4.2.0 (2017-08-08)
==================

- Add support for Python 3.5 and 3.6.

- Drop support for Python 2.6 and 3.3.

- Convert doctests to Sphinx documentation, including building docs
  and running doctest snippets under ``tox``.


4.1.0 (2014-12-26)
==================

- Add support for PyPy.  PyPy3 support is blocked on release of fix for:
  https://bitbucket.org/pypy/pypy/issue/1946

- Add support for Python 3.4.

- Add support for testing on Travis.


4.0.0 (2013-02-28)
==================

- Add support for Python 3.3.

- Drop support for Python 2.4 and 2.5.


3.5.3 (2010-09-25)
==================

- Add test extra to declare test dependency on ``zope.testing``.


3.5.2 (2010-05-24)
==================

- Fix unit tests broken under Python 2.4 by the switch to the standard
  library ``doctest`` module.


3.5.1 (2010-04-30)
==================

- Prefer the standard library's ``doctest`` module to the one from
  ``zope.testing``.


3.5.0 (2009-02-04)
==================

- Add support to bootstrap on Jython.

- Add reference documentation.


3.4.0 (2007-07-19)
==================

- Finish release of ``zope.deferredimport``.


3.4.0b1 (2007-07-09)
====================

- Initial release as a separate project, corresponding to the
  ``zope.deferredimport`` from Zope 3.4.0b1.
