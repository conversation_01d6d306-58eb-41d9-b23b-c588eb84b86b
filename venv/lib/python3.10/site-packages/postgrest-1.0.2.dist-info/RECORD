postgrest-1.0.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
postgrest-1.0.2.dist-info/LICENSE,sha256=M03Wgg4urqsgZOfFkAG4EFZnKKKKQafB2_abvuF9CTY,1065
postgrest-1.0.2.dist-info/METADATA,sha256=57a-6ugBlA4YlBx01gBiaNSBD_j8P0CRCParBaap9ds,3458
postgrest-1.0.2.dist-info/RECORD,,
postgrest-1.0.2.dist-info/WHEEL,sha256=Nq82e9rUAnEjt98J6MlVmMCZb-t9cYE2Ir1kpBmnWfs,88
postgrest/__init__.py,sha256=30MCuUqc_tEQZxJFN01F9DXucq1iMMKDeMoW43TYSwk,950
postgrest/__pycache__/__init__.cpython-310.pyc,,
postgrest/__pycache__/base_client.cpython-310.pyc,,
postgrest/__pycache__/base_request_builder.cpython-310.pyc,,
postgrest/__pycache__/constants.cpython-310.pyc,,
postgrest/__pycache__/deprecated_client.cpython-310.pyc,,
postgrest/__pycache__/deprecated_get_request_builder.cpython-310.pyc,,
postgrest/__pycache__/exceptions.cpython-310.pyc,,
postgrest/__pycache__/types.cpython-310.pyc,,
postgrest/__pycache__/utils.cpython-310.pyc,,
postgrest/__pycache__/version.cpython-310.pyc,,
postgrest/_async/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_async/__pycache__/__init__.cpython-310.pyc,,
postgrest/_async/__pycache__/client.cpython-310.pyc,,
postgrest/_async/__pycache__/request_builder.cpython-310.pyc,,
postgrest/_async/client.py,sha256=uM-EwKDQmxCpg7gTRGBH-e0yyWK4LHkvHKIdLYdHzAw,4742
postgrest/_async/request_builder.py,sha256=EE_R9RTRoHhb1DtRoJYOC6BlXnB9D6lGYhsw02Fx_Ho,14234
postgrest/_sync/__init__.py,sha256=U4S_2y3zgLZVfMenHRaJFBW8yqh2mUBuI291LGQVOJ8,35
postgrest/_sync/__pycache__/__init__.cpython-310.pyc,,
postgrest/_sync/__pycache__/client.cpython-310.pyc,,
postgrest/_sync/__pycache__/request_builder.cpython-310.pyc,,
postgrest/_sync/client.py,sha256=HirS2dLy8a9DcSf3qUKw7Xo7KeIfRawkxPu2Yig65yM,4694
postgrest/_sync/request_builder.py,sha256=8BiST3DGzMzfi_Y_WEUp4NrqDcWZ_p4UIAtz1-NEtIw,14158
postgrest/base_client.py,sha256=NBQ-50HyRsXHjwSqfHkSYi1PJBINy0EtCMUj8wdDhtM,2230
postgrest/base_request_builder.py,sha256=ba0dvXbIBMGgnROCpt19xX6M66nz4JoYyLHi6kR8kT0,23769
postgrest/constants.py,sha256=VZrlQtgGV-qwcjwqhlJOZBhPHzbSICjDJbabcAlPMUY,153
postgrest/deprecated_client.py,sha256=6sC3m36fiUrwORHYOSyXjUXC21f4BfdTCEMgIedN8qE,416
postgrest/deprecated_get_request_builder.py,sha256=ycFiTJSfO4sWlQGSQMPVWj3oXLQAv1FVIi0vgT8o26A,429
postgrest/exceptions.py,sha256=xv8MCJaop_XDb6ux8waYMff3RCuCcUZnSCC1uUq7AXo,1887
postgrest/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
postgrest/types.py,sha256=o1CCqNb7TAfdaCoYBF137kDio37dWszGNRaQJsDhHqY,986
postgrest/utils.py,sha256=BIRLcBUix4vPqJq_6R0Lu_pl2G1qStt0q8Z0L2CMDaM,1953
postgrest/version.py,sha256=zxZW99SgQjondU32dukwqPd-G7Ppgqv35TRvpBD4iL0,52
