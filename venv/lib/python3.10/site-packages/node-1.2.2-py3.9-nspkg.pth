import sys, types, os;p = os.path.join(sys._getframe(1).f_locals['sitedir'], *('node',));importlib = __import__('importlib.util');__import__('importlib.machinery');m = sys.modules.setdefault('node', importlib.util.module_from_spec(importlib.machinery.PathFinder.find_spec('node', [os.path.dirname(p)])));m = m or sys.modules.setdefault('node', types.ModuleType('node'));mp = (m or []) and m.__dict__.setdefault('__path__',[]);(p not in mp) and mp.append(p)
