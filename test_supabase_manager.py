#!/usr/bin/env python3
"""
Test the Supabase Database Manager

Tests all the key functionality of the new Supabase-based database manager.
"""

import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.append(str(Path(__file__).parent))

from db.supabase_manager import SupabaseDatabaseManager

def test_supabase_manager():
    """Test the Supabase database manager functionality."""
    print("🧪 Testing Supabase Database Manager")
    print("=" * 50)
    
    try:
        # Initialize manager
        print("1️⃣ Initializing Supabase manager...")
        with SupabaseDatabaseManager() as db:
            print("✅ Connected successfully")
            
            # Test 1: Add ZIP file
            print("\n2️⃣ Testing ZIP file management...")
            zip_id = db.add_zip_file(
                filename="test_2025q1.zip",
                url="https://example.com/test.zip",
                date_str="20250101",
                file_size=1024000,
                status="downloaded"
            )
            print(f"✅ Added ZIP file with ID: {zip_id}")
            
            # Get ZIP file
            zip_record = db.get_zip_file("test_2025q1.zip")
            print(f"✅ Retrieved ZIP file: {zip_record['filename']}")
            
            # Test 2: Add Form D filing
            print("\n3️⃣ Testing Form D filing management...")
            filing_data = {
                "accessionNumber": "0001234567-25-000001",
                "issuerName": "Test AI Startup Inc.",
                "filingDate": "2025-01-15",
                "offeringAmount": 5000000.00,
                "industryGroup": "Technology",
                "issuerCity": "San Francisco",
                "issuerState": "CA",
                "issuerZip": "94105",
                "offeringType": "Equity",
                "minimumInvestment": 100000.00,
                "totalAmountSold": 2000000.00,
                "totalRemaining": 3000000.00,
                "description": "AI-powered SEC Form D analysis platform"
            }
            
            filing_id = db.add_form_d_filing(
                filing_data=filing_data,
                source_zip_id=zip_id,
                source_file="formD_20250115.json"
            )
            print(f"✅ Added Form D filing with ID: {filing_id}")
            
            # Get filing
            filing_record = db.get_form_d_filing("0001234567-25-000001")
            print(f"✅ Retrieved filing: {filing_record['issuer_name']}")
            
            # Test 3: Search filings
            print("\n4️⃣ Testing filing search...")
            search_results = db.search_filings(
                issuer_name="Test AI",
                industry_group="Technology",
                min_amount=1000000,
                limit=10
            )
            print(f"✅ Found {len(search_results)} matching filings")
            
            # Test 4: Get statistics
            print("\n5️⃣ Testing database statistics...")
            stats = db.get_stats()
            print("✅ Database statistics:")
            for key, value in stats.items():
                if isinstance(value, (int, float)):
                    if 'amount' in key and value:
                        print(f"   📊 {key}: ${value:,.2f}")
                    else:
                        print(f"   📊 {key}: {value}")
            
            # Test 5: JSONB query (PostgreSQL-specific feature)
            print("\n6️⃣ Testing JSONB queries...")
            cursor = db.get_cursor()
            cursor.execute("""
                SELECT issuer_name, json_data->>'description' as description
                FROM form_d_filings 
                WHERE json_data->>'industryGroup' = 'Technology'
            """)
            results = cursor.fetchall()
            print(f"✅ JSONB query found {len(results)} technology filings")
            for result in results:
                print(f"   🏢 {result['issuer_name']}: {result['description']}")
            
            # Cleanup test data
            print("\n7️⃣ Cleaning up test data...")
            cursor.execute("DELETE FROM form_d_filings WHERE accession_number = %s", 
                          ("0001234567-25-000001",))
            cursor.execute("DELETE FROM zip_files WHERE filename = %s", 
                          ("test_2025q1.zip",))
            db.commit()
            print("✅ Test data cleaned up")
            
        print("\n🎉 All tests passed! Supabase manager is working correctly.")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_supabase_manager()
    sys.exit(0 if success else 1)
