#!/usr/bin/env python3
"""
Comprehensive Supabase Connection Diagnostics

Tests multiple connection methods and diagnoses common issues:
- IPv4/IPv6 compatibility
- SSL configuration
- DNS resolution
- Connection pooling
- Network connectivity
"""

import os
import socket
import ssl
import time
from pathlib import Path
from urllib.parse import urlparse
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_network_connectivity():
    """Test basic network connectivity to Supabase."""
    try:
        url = os.environ.get('SUPABASE_URL', '')
        if not url or url.startswith('your_'):
            print("⚠️ Skipping network test - no valid SUPABASE_URL")
            return False

        parsed = urlparse(url)
        hostname = parsed.hostname
        port = 443  # HTTPS port

        print(f"🌐 Testing network connectivity to {hostname}:{port}")

        # Test IPv4 connection
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((hostname, port))
            sock.close()

            if result == 0:
                print("✅ IPv4 connection successful")
                return True
            else:
                print(f"❌ IPv4 connection failed (error code: {result})")
        except Exception as e:
            print(f"❌ IPv4 connection error: {e}")

        # Test IPv6 connection
        try:
            sock = socket.socket(socket.AF_INET6, socket.SOCK_STREAM)
            sock.settimeout(10)
            result = sock.connect_ex((hostname, port))
            sock.close()

            if result == 0:
                print("✅ IPv6 connection successful")
                return True
            else:
                print(f"❌ IPv6 connection failed (error code: {result})")
        except Exception as e:
            print(f"❌ IPv6 connection error: {e}")

        return False

    except Exception as e:
        print(f"❌ Network connectivity test failed: {e}")
        return False

def test_dns_resolution():
    """Test DNS resolution for Supabase hostname."""
    try:
        url = os.environ.get('SUPABASE_URL', '')
        if not url or url.startswith('your_'):
            return False

        parsed = urlparse(url)
        hostname = parsed.hostname

        print(f"🔍 Testing DNS resolution for {hostname}")

        # Get IPv4 addresses
        try:
            ipv4_addresses = socket.getaddrinfo(hostname, None, socket.AF_INET)
            ipv4_ips = [addr[4][0] for addr in ipv4_addresses]
            print(f"✅ IPv4 addresses: {', '.join(ipv4_ips)}")
        except Exception as e:
            print(f"❌ IPv4 DNS resolution failed: {e}")

        # Get IPv6 addresses
        try:
            ipv6_addresses = socket.getaddrinfo(hostname, None, socket.AF_INET6)
            ipv6_ips = [addr[4][0] for addr in ipv6_addresses]
            print(f"✅ IPv6 addresses: {', '.join(ipv6_ips[:2])}...")  # Show first 2
        except Exception as e:
            print(f"❌ IPv6 DNS resolution failed: {e}")

        return True

    except Exception as e:
        print(f"❌ DNS resolution test failed: {e}")
        return False

def test_env_vars():
    """Test if all required environment variables are set."""
    required_vars = [
        'SUPABASE_URL',
        'SUPABASE_SERVICE_ROLE_KEY',
        'POSTGRES_CONNECTION_STRING'
    ]

    missing = []
    for var in required_vars:
        if not os.environ.get(var) or os.environ.get(var).startswith('your_'):
            missing.append(var)

    if missing:
        print("❌ Missing or placeholder environment variables:")
        for var in missing:
            print(f"   - {var}")
        print("\nPlease update your .env file with actual Supabase credentials")
        return False

    print("✅ All environment variables are set")
    return True

def test_supabase_connection():
    """Test Supabase connection."""
    try:
        from supabase import create_client
        
        url = os.environ.get('SUPABASE_URL')
        key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')
        
        supabase = create_client(url, key)
        
        # Test a simple query
        result = supabase.table('_realtime_schema_migrations').select('*').limit(1).execute()
        
        print("✅ Supabase client connection successful")
        return True
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def test_postgres_connection_variants():
    """Test multiple PostgreSQL connection methods."""
    conn_str = os.environ.get('POSTGRES_CONNECTION_STRING')
    if not conn_str or conn_str.startswith('postgresql://postgres:your_'):
        print("⚠️ Skipping PostgreSQL test - no valid connection string")
        return False

    # Test different connection string variants
    variants = [
        # Original connection string
        conn_str,
        # Force IPv4
        conn_str + "?options=-c%20default_transaction_isolation=read_committed",
        # Add SSL and IPv4 preferences
        conn_str + "?sslmode=require&connect_timeout=10",
        # Connection pooling URL (if available)
        conn_str.replace('db.', 'aws-0-us-east-1.pooler.') if 'db.' in conn_str else conn_str
    ]

    for i, variant in enumerate(variants):
        try:
            import psycopg2

            print(f"🔌 Testing PostgreSQL connection variant {i+1}...")

            # Add connection timeout
            conn = psycopg2.connect(variant, connect_timeout=10)
            cursor = conn.cursor()

            cursor.execute("SELECT version();")
            version = cursor.fetchone()[0]

            cursor.execute("SELECT inet_server_addr();")
            server_ip = cursor.fetchone()[0]

            conn.close()

            print(f"✅ PostgreSQL connection successful (variant {i+1})")
            print(f"   Server IP: {server_ip}")
            print(f"   Version: {version[:50]}...")
            return True

        except Exception as e:
            print(f"❌ PostgreSQL variant {i+1} failed: {e}")
            continue

    return False

def test_supabase_rest_api():
    """Test Supabase REST API as alternative to direct PostgreSQL."""
    try:
        import requests

        url = os.environ.get('SUPABASE_URL')
        key = os.environ.get('SUPABASE_SERVICE_ROLE_KEY')

        if not url or not key:
            return False

        # Test REST API endpoint
        headers = {
            'apikey': key,
            'Authorization': f'Bearer {key}',
            'Content-Type': 'application/json'
        }

        # Try to access a system table
        response = requests.get(
            f"{url}/rest/v1/information_schema.tables?select=table_name&limit=1",
            headers=headers,
            timeout=10
        )

        if response.status_code == 200:
            print("✅ Supabase REST API connection successful")
            return True
        else:
            print(f"❌ Supabase REST API failed: {response.status_code} - {response.text}")
            return False

    except Exception as e:
        print(f"❌ Supabase REST API test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 Comprehensive Supabase Connection Diagnostics")
    print("=" * 60)

    # Test 1: Environment variables
    print("\n1️⃣ Testing Environment Variables")
    print("-" * 30)
    env_ok = test_env_vars()

    if not env_ok:
        print("\n❌ Cannot proceed without valid environment variables")
        return False

    # Test 2: Network connectivity
    print("\n2️⃣ Testing Network Connectivity")
    print("-" * 30)
    network_ok = test_network_connectivity()

    # Test 3: DNS resolution
    print("\n3️⃣ Testing DNS Resolution")
    print("-" * 30)
    dns_ok = test_dns_resolution()

    # Test 4: Supabase client
    print("\n4️⃣ Testing Supabase Client")
    print("-" * 30)
    client_ok = test_supabase_connection()

    # Test 5: PostgreSQL direct connection variants
    print("\n5️⃣ Testing PostgreSQL Connection Variants")
    print("-" * 30)
    postgres_ok = test_postgres_connection_variants()

    # Test 6: Supabase REST API (fallback)
    print("\n6️⃣ Testing Supabase REST API (Fallback)")
    print("-" * 30)
    rest_ok = test_supabase_rest_api()

    # Summary
    print("\n" + "=" * 60)
    print("📊 DIAGNOSTIC SUMMARY")
    print("=" * 60)

    tests = [
        ("Environment Variables", env_ok),
        ("Network Connectivity", network_ok),
        ("DNS Resolution", dns_ok),
        ("Supabase Client", client_ok),
        ("PostgreSQL Direct", postgres_ok),
        ("REST API Fallback", rest_ok)
    ]

    for test_name, result in tests:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25} {status}")

    # Determine next steps
    if postgres_ok or rest_ok:
        print("\n🎉 Connection successful! Ready for migration.")
        print("\nRecommended next steps:")
        if postgres_ok:
            print("1. Run: python db/migrate_to_supabase.py")
            print("2. Use direct PostgreSQL connection for best performance")
        else:
            print("1. Use Supabase REST API for now")
            print("2. Investigate PostgreSQL connection issues later")
    else:
        print("\n⚠️ Connection issues detected. Recommendations:")
        if not network_ok:
            print("• Check your internet connection")
            print("• Verify firewall settings")
            print("• Try connecting from a different network")
        if not postgres_ok:
            print("• Consider using connection pooling URL")
            print("• Check if your network supports IPv6")
            print("• Verify Supabase project is active")

    return postgres_ok or rest_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
